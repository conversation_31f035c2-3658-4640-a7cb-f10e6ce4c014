package utils

import (
	"fmt"
	"math/rand"
	"regexp"
	"strings"
	"time"
)

// StringUtils 字符串工具集
type StringUtils struct{}

// NewStringUtils 创建字符串工具集
func NewStringUtils() *StringUtils {
	return &StringUtils{}
}

// GenerateID 生成唯一ID
func (su *StringUtils) GenerateID(prefix string) string {
	timestamp := time.Now().Unix()
	random := rand.Intn(10000)
	if prefix == "" {
		return fmt.Sprintf("%d_%04d", timestamp, random)
	}
	return fmt.Sprintf("%s_%d_%04d", prefix, timestamp, random)
}

// TruncateString 截断字符串
func (su *StringUtils) TruncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}

	if maxLen <= 3 {
		return s[:maxLen]
	}

	return s[:maxLen-3] + "..."
}

// SanitizeString 清理字符串
func (su *StringUtils) SanitizeString(s string) string {
	// 移除控制字符
	re := regexp.MustCompile(`[\x00-\x1f\x7f]`)
	s = re.ReplaceAllString(s, "")

	// 移除多余的空白字符
	s = strings.TrimSpace(s)
	re = regexp.MustCompile(`\s+`)
	s = re.ReplaceAllString(s, " ")

	return s
}

// ParseKeyValue 解析键值对字符串
func (su *StringUtils) ParseKeyValue(s string, separator string) map[string]string {
	result := make(map[string]string)

	if separator == "" {
		separator = "="
	}

	lines := strings.Split(s, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, separator, 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			result[key] = value
		}
	}

	return result
}

// IsValidIP 检查是否为有效IP地址
func (su *StringUtils) IsValidIP(ip string) bool {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}

	for _, part := range parts {
		if len(part) == 0 || len(part) > 3 {
			return false
		}

		// 检查是否为数字
		for _, char := range part {
			if char < '0' || char > '9' {
				return false
			}
		}

		// 检查范围
		if part[0] == '0' && len(part) > 1 {
			return false // 不允许前导零
		}

		// 转换为数字检查范围
		num := 0
		for _, char := range part {
			num = num*10 + int(char-'0')
		}
		if num > 255 {
			return false
		}
	}

	return true
}

// IsValidHostname 检查是否为有效主机名
func (su *StringUtils) IsValidHostname(hostname string) bool {
	if len(hostname) == 0 || len(hostname) > 253 {
		return false
	}

	// 主机名不能以点开始或结束
	if strings.HasPrefix(hostname, ".") || strings.HasSuffix(hostname, ".") {
		return false
	}

	// 检查每个标签
	labels := strings.Split(hostname, ".")
	for _, label := range labels {
		if len(label) == 0 || len(label) > 63 {
			return false
		}

		// 标签不能以连字符开始或结束
		if strings.HasPrefix(label, "-") || strings.HasSuffix(label, "-") {
			return false
		}

		// 检查字符
		for _, char := range label {
			if !((char >= 'a' && char <= 'z') ||
				(char >= 'A' && char <= 'Z') ||
				(char >= '0' && char <= '9') ||
				char == '-') {
				return false
			}
		}
	}

	return true
}

// ContainsIgnoreCase 忽略大小写的字符串包含检查
func (su *StringUtils) ContainsIgnoreCase(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

// SplitAndTrim 分割字符串并去除空白
func (su *StringUtils) SplitAndTrim(s, sep string) []string {
	parts := strings.Split(s, sep)
	var result []string

	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}

	return result
}

// JoinNonEmpty 连接非空字符串
func (su *StringUtils) JoinNonEmpty(parts []string, sep string) string {
	var nonEmpty []string

	for _, part := range parts {
		if strings.TrimSpace(part) != "" {
			nonEmpty = append(nonEmpty, part)
		}
	}

	return strings.Join(nonEmpty, sep)
}

// EscapeHTML 转义HTML特殊字符
func (su *StringUtils) EscapeHTML(s string) string {
	s = strings.ReplaceAll(s, "&", "&amp;")
	s = strings.ReplaceAll(s, "<", "&lt;")
	s = strings.ReplaceAll(s, ">", "&gt;")
	s = strings.ReplaceAll(s, "\"", "&quot;")
	s = strings.ReplaceAll(s, "'", "&#39;")
	return s
}

// UnescapeHTML 反转义HTML特殊字符
func (su *StringUtils) UnescapeHTML(s string) string {
	s = strings.ReplaceAll(s, "&amp;", "&")
	s = strings.ReplaceAll(s, "&lt;", "<")
	s = strings.ReplaceAll(s, "&gt;", ">")
	s = strings.ReplaceAll(s, "&quot;", "\"")
	s = strings.ReplaceAll(s, "&#39;", "'")
	return s
}

// MaskSensitiveInfo 掩码敏感信息
func (su *StringUtils) MaskSensitiveInfo(s string) string {
	if len(s) <= 4 {
		return strings.Repeat("*", len(s))
	}

	// 显示前2位和后2位，中间用*代替
	return s[:2] + strings.Repeat("*", len(s)-4) + s[len(s)-2:]
}

// FormatList 格式化列表为字符串
func (su *StringUtils) FormatList(items []string, separator string) string {
	if separator == "" {
		separator = ", "
	}
	return strings.Join(items, separator)
}

// PadLeft 左填充字符串
func (su *StringUtils) PadLeft(s string, length int, padChar rune) string {
	if len(s) >= length {
		return s
	}

	padding := strings.Repeat(string(padChar), length-len(s))
	return padding + s
}

// PadRight 右填充字符串
func (su *StringUtils) PadRight(s string, length int, padChar rune) string {
	if len(s) >= length {
		return s
	}

	padding := strings.Repeat(string(padChar), length-len(s))
	return s + padding
}

// RemoveEmptyLines 移除空行
func (su *StringUtils) RemoveEmptyLines(s string) string {
	lines := strings.Split(s, "\n")
	var nonEmptyLines []string

	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			nonEmptyLines = append(nonEmptyLines, line)
		}
	}

	return strings.Join(nonEmptyLines, "\n")
}

// 全局字符串工具实例
var StringUtil = NewStringUtils()
