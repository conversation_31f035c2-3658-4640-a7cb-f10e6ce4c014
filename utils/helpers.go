package utils

import (
	"sort"
	"strings"
	"time"

	"lcheck/data"
)

// Helpers 工具集合 - 模块化门面，委托给专门的工具模块
type Helpers struct {
	StringUtil     *StringUtils
	TimeUtil       *TimeUtils
	ValidationUtil *ValidationUtils
	FormatUtil     *FormatUtils
}

// NewHelpers 创建工具集合
func NewHelpers() *Helpers {
	return &Helpers{
		StringUtil:     NewStringUtils(),
		TimeUtil:       NewTimeUtils(),
		ValidationUtil: NewValidationUtils(),
		FormatUtil:     NewFormatUtils(),
	}
}

// ========== 委托给各工具模块的便捷方法 ==========

// GenerateID 生成唯一ID - 委托给字符串工具
func GenerateID(prefix string) string {
	return StringUtil.GenerateID(prefix)
}

// FormatDuration 格式化时间间隔 - 委托给时间工具
func FormatDuration(d time.Duration) string {
	return TimeUtil.FormatDuration(d)
}

// FormatTime 格式化时间 - 委托给时间工具
func FormatTime(t time.Time) string {
	return TimeUtil.FormatTime(t)
}

// FormatTimeShort 格式化时间（短格式） - 委托给时间工具
func FormatTimeShort(t time.Time) string {
	return TimeUtil.FormatTimeShort(t)
}

// TruncateString 截断字符串 - 委托给字符串工具
func TruncateString(s string, maxLen int) string {
	return StringUtil.TruncateString(s, maxLen)
}

// ValidateHostInfo 验证主机信息 - 委托给验证工具
func ValidateHostInfo(host data.HostInfo) []string {
	return ValidationUtil.ValidateHostInfo(host)
}

// ValidateHostGroup 验证主机组信息 - 委托给验证工具
func ValidateHostGroup(group data.HostGroup) []string {
	return ValidationUtil.ValidateHostGroup(group)
}

// SanitizeString 清理字符串 - 委托给字符串工具
func SanitizeString(s string) string {
	return StringUtil.SanitizeString(s)
}

// ParseKeyValue 解析键值对字符串 - 委托给字符串工具
func ParseKeyValue(s string, separator string) map[string]string {
	return StringUtil.ParseKeyValue(s, separator)
}

// FormatBytes 格式化字节数 - 委托给格式化工具
func FormatBytes(bytes int64) string {
	return FormatUtil.FormatBytes(bytes)
}

// IsValidIP 检查是否为有效IP地址 - 委托给字符串工具
func IsValidIP(ip string) bool {
	return StringUtil.IsValidIP(ip)
}

// IsValidHostname 检查是否为有效主机名 - 委托给字符串工具
func IsValidHostname(hostname string) bool {
	return StringUtil.IsValidHostname(hostname)
}

// GetRiskLevel 根据分数获取风险等级 - 委托给格式化工具
func GetRiskLevel(percentage float64) string {
	return FormatUtil.GetRiskLevel(percentage)
}

// GetRiskColor 获取风险等级对应的颜色 - 委托给格式化工具
func GetRiskColor(risk string) string {
	return FormatUtil.GetRiskColor(risk)
}

// GetStatusColor 获取状态对应的颜色 - 委托给格式化工具
func GetStatusColor(status string) string {
	return FormatUtil.GetStatusColor(status)
}

// ========== 数据处理相关方法 ==========

// CalculateScore 计算总分
func CalculateScore(results []data.BaselineCheckResult) (int, int) {
	totalScore := 0
	validResults := 0
	for _, result := range results {
		if result.Score >= 0 {
			totalScore += result.Score
			validResults++
		}
	}
	return totalScore, validResults
}

// CalculateScorePercentage 计算分数百分比
func CalculateScorePercentage(totalScore, maxScore int) float64 {
	if maxScore == 0 {
		return 0
	}
	return float64(totalScore) / float64(maxScore) * 100
}

// FilterHosts 过滤主机列表
func FilterHosts(hosts []data.HostInfo, filter string) []data.HostInfo {
	if filter == "" {
		return hosts
	}

	var filtered []data.HostInfo
	filter = strings.ToLower(filter)

	for _, host := range hosts {
		if strings.Contains(strings.ToLower(host.Name), filter) ||
			strings.Contains(strings.ToLower(host.Host), filter) ||
			strings.Contains(strings.ToLower(host.Description), filter) {
			filtered = append(filtered, host)
		}
	}

	return filtered
}

// FilterGroups 过滤主机组列表
func FilterGroups(groups []data.HostGroup, filter string) []data.HostGroup {
	if filter == "" {
		return groups
	}

	var filtered []data.HostGroup
	filter = strings.ToLower(filter)

	for _, group := range groups {
		if strings.Contains(strings.ToLower(group.Name), filter) ||
			strings.Contains(strings.ToLower(group.Description), filter) {
			filtered = append(filtered, group)
		}
	}

	return filtered
}

// SortHostsByName 按名称排序主机
func SortHostsByName(hosts []data.HostInfo) []data.HostInfo {
	sorted := make([]data.HostInfo, len(hosts))
	copy(sorted, hosts)

	sort.Slice(sorted, func(i, j int) bool {
		return strings.ToLower(sorted[i].Name) < strings.ToLower(sorted[j].Name)
	})

	return sorted
}

// SortGroupsByName 按名称排序主机组
func SortGroupsByName(groups []data.HostGroup) []data.HostGroup {
	sorted := make([]data.HostGroup, len(groups))
	copy(sorted, groups)

	sort.Slice(sorted, func(i, j int) bool {
		return strings.ToLower(sorted[i].Name) < strings.ToLower(sorted[j].Name)
	})

	return sorted
}

// GetCheckCategorySummary 获取检查类别汇总
func GetCheckCategorySummary(results []data.BaselineCheckResult) map[string]map[string]int {
	summary := make(map[string]map[string]int)

	for _, result := range results {
		if summary[result.Category] == nil {
			summary[result.Category] = make(map[string]int)
		}
		summary[result.Category][result.Status]++
	}

	return summary
}

// GetRiskSummary 获取风险等级汇总
func GetRiskSummary(results []data.BaselineCheckResult) map[string]int {
	summary := make(map[string]int)

	for _, result := range results {
		percentage := float64(result.Score)
		risk := GetRiskLevel(percentage)
		summary[risk]++
	}

	return summary
}

// ExportToCSV 导出为CSV格式
func ExportToCSV(results []data.ScanResult) string {
	var lines []string

	// CSV标题行
	lines = append(lines, "主机名,主机地址,扫描时间,状态,总分,检查项数量,持续时间")

	// 数据行
	for _, result := range results {
		line := strings.Join([]string{
			result.HostName,
			result.Host,
			FormatTime(result.StartTime),
			result.Status,
			FormatUtil.FormatScore(result.TotalScore),
			string(rune(len(result.CheckResults))),
			FormatDuration(result.Duration),
		}, ",")
		lines = append(lines, line)
	}

	return strings.Join(lines, "\n")
}

// 全局工具集合实例
var Helper = NewHelpers()
