package ui

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"

	"lcheck/core"
	"lcheck/data"
	"lcheck/services"
	"lcheck/ui/components"
)

// ScanTab 扫描标签页 - 模块化门面，委托给专门的UI组件
type ScanTab struct {
	// 服务层依赖
	taskService   *services.TaskService
	hostService   *services.HostService
	exportService *services.ExportService

	// 基础依赖
	updateStatus func(string)
	window       fyne.Window
	app          *App

	// UI组件 - 使用专门的组件
	content      *fyne.Container
	toolbar      *components.ScanToolbar
	taskTable    *components.ScanTable
	progressView *components.ScanProgress

	// 当前数据
	selectedTask *data.ScanTask
}

// NewScanTab 创建扫描标签页 - 使用模块化组件
func NewScanTab(storage data.Storage, scanner *core.Scanner, updateStatus func(string)) *ScanTab {
	// 初始化服务层
	taskService := services.NewTaskService(storage, scanner)
	sshClient := scanner.GetSSHClient() // 获取SSH客户端
	hostService := services.NewHostService(storage, sshClient)
	exportService := services.NewExportService(storage)

	tab := &ScanTab{
		taskService:   taskService,
		hostService:   hostService,
		exportService: exportService,
		updateStatus:  updateStatus,
	}

	tab.buildUI()
	tab.setupCallbacks()
	tab.loadTasks()

	return tab
}

// SetWindow 设置窗口引用
func (st *ScanTab) SetWindow(window fyne.Window) {
	st.window = window
}

// SetApp 设置应用引用
func (st *ScanTab) SetApp(app *App) {
	st.app = app
}

// buildUI 构建UI - 使用专门的组件
func (st *ScanTab) buildUI() {
	// 创建专门的UI组件
	st.toolbar = components.NewScanToolbar()
	st.taskTable = components.NewScanTable()
	st.progressView = components.NewScanProgress()

	// 创建主布局
	st.content = container.NewBorder(
		st.toolbar.GetContainer(),      // 顶部工具栏
		st.progressView.GetContainer(), // 底部进度显示
		nil, nil,                       // 左右为空
		st.taskTable.GetContainer(), // 中间任务表格
	)
}

// setupCallbacks 设置回调函数 - 连接组件和业务逻辑
func (st *ScanTab) setupCallbacks() {
	// 工具栏回调
	st.toolbar.SetOnCreateTask(st.handleCreateTask)
	st.toolbar.SetOnDeleteTask(st.handleDeleteTask)
	st.toolbar.SetOnStartTask(st.handleStartTask)
	st.toolbar.SetOnStopTask(st.handleStopTask)
	st.toolbar.SetOnExportTask(st.handleExportTask)
	st.toolbar.SetOnRefreshData(st.handleRefreshData)

	// 任务表格回调
	st.taskTable.SetOnSelection(st.handleTaskSelection)
	st.taskTable.SetOnDoubleClick(st.handleTaskDoubleClick)

	// 进度组件回调
	st.progressView.SetOnProgressUpdate(st.handleProgressUpdate)
}

// GetContent 获取内容容器
func (st *ScanTab) GetContent() *fyne.Container {
	return st.content
}

// ========== 数据操作方法 ==========

// loadTasks 加载任务列表 - 委托给任务服务
func (st *ScanTab) loadTasks() {
	tasks, err := st.taskService.LoadTasks()
	if err != nil {
		st.showError("加载任务失败", err)
		return
	}

	st.taskTable.SetTasks(tasks)
	st.updateStatus(fmt.Sprintf("已加载 %d 个任务", len(tasks)))
}

// refreshTasks 刷新任务列表
func (st *ScanTab) refreshTasks() {
	st.loadTasks()
}

// RefreshData 刷新数据（公共方法）
func (st *ScanTab) RefreshData() {
	st.refreshTasks()
}

// ========== 事件处理方法 ==========

// handleCreateTask 处理创建任务
func (st *ScanTab) handleCreateTask() {
	st.showCreateTaskDialog()
}

// handleDeleteTask 处理删除任务
func (st *ScanTab) handleDeleteTask() {
	if st.selectedTask == nil {
		st.showInfo("提示", "请先选择要删除的任务")
		return
	}

	st.showDeleteConfirmDialog()
}

// handleStartTask 处理启动任务
func (st *ScanTab) handleStartTask() {
	if st.selectedTask == nil {
		st.showInfo("提示", "请先选择要启动的任务")
		return
	}

	if st.selectedTask.Status != "待执行" {
		st.showInfo("提示", "只能启动待执行状态的任务")
		return
	}

	st.startTask(st.selectedTask)
}

// handleStopTask 处理停止任务
func (st *ScanTab) handleStopTask() {
	if st.selectedTask == nil {
		st.showInfo("提示", "请先选择要停止的任务")
		return
	}

	if st.selectedTask.Status != "运行中" {
		st.showInfo("提示", "只能停止运行中的任务")
		return
	}

	st.stopTask(st.selectedTask)
}

// handleExportTask 处理导出任务
func (st *ScanTab) handleExportTask() {
	if st.selectedTask == nil {
		st.showInfo("提示", "请先选择要导出的任务")
		return
	}

	if st.selectedTask.Status != "已完成" {
		st.showInfo("提示", "只能导出已完成的任务")
		return
	}

	st.exportTask(st.selectedTask)
}

// handleRefreshData 处理刷新数据
func (st *ScanTab) handleRefreshData() {
	st.refreshTasks()
}

// handleTaskSelection 处理任务选择
func (st *ScanTab) handleTaskSelection(task *data.ScanTask) {
	st.selectedTask = task
	st.toolbar.SetSelectedTask(task)

	if task != nil {
		st.updateStatus(fmt.Sprintf("已选择任务: %s", task.Name))
	} else {
		st.updateStatus("未选择任务")
	}
}

// handleTaskDoubleClick 处理任务双击
func (st *ScanTab) handleTaskDoubleClick(task *data.ScanTask) {
	if task == nil {
		return
	}

	// 双击显示任务详情
	st.showTaskDetails(task)
}

// handleProgressUpdate 处理进度更新
func (st *ScanTab) handleProgressUpdate(progress float64) {
	// 可以在这里处理进度更新的额外逻辑
	st.updateStatus(fmt.Sprintf("任务进度: %.1f%%", progress))
}

// ========== 业务逻辑方法 ==========

// startTask 启动任务
func (st *ScanTab) startTask(task *data.ScanTask) {
	// 开始进度显示
	st.progressView.StartProgress(task)

	// 委托给任务服务执行
	go func() {
		err := st.taskService.StartTask(task)
		if err != nil {
			st.progressView.CompleteProgress(false, err.Error())
			st.showError("启动任务失败", err)
		} else {
			st.progressView.CompleteProgress(true, "任务启动成功")
			st.refreshTasks()
		}
	}()
}

// stopTask 停止任务
func (st *ScanTab) stopTask(task *data.ScanTask) {
	err := st.taskService.StopTask(task)
	if err != nil {
		st.showError("停止任务失败", err)
		return
	}

	st.progressView.StopProgress()
	st.refreshTasks()
	st.updateStatus("任务已停止")
}

// exportTask 导出任务
func (st *ScanTab) exportTask(task *data.ScanTask) {
	// 委托给导出服务
	options := services.ExportOptions{
		Format:     "html",
		Content:    "全部内容",
		IncludeRaw: true,
	}
	content, err := st.exportService.ExportTask(task, options)
	if err != nil {
		st.showError("导出任务失败", err)
		return
	}

	// 保存文件
	fileName := st.exportService.GenerateFileName(task, "html")
	filePath := "./exports/" + fileName
	err = st.exportService.SaveExportFile(filePath, content)
	if err != nil {
		st.showError("保存导出文件失败", err)
		return
	}

	st.showInfo("导出成功", fmt.Sprintf("任务报告已导出到: %s", filePath))
}

// ========== 对话框方法 ==========

// showCreateTaskDialog 显示创建任务对话框
func (st *ScanTab) showCreateTaskDialog() {
	// 这里应该显示创建任务的对话框
	// 为简化，暂时显示一个简单的信息对话框
	st.showInfo("创建任务", "创建任务功能待实现")
}

// showDeleteConfirmDialog 显示删除确认对话框
func (st *ScanTab) showDeleteConfirmDialog() {
	dialog.ShowConfirm("确认删除",
		fmt.Sprintf("确定要删除任务 '%s' 吗？", st.selectedTask.Name),
		func(confirmed bool) {
			if confirmed {
				st.deleteTask(st.selectedTask)
			}
		}, st.window)
}

// showTaskDetails 显示任务详情
func (st *ScanTab) showTaskDetails(task *data.ScanTask) {
	details := fmt.Sprintf("任务名称: %s\n状态: %s\n进度: %.1f%%\n主机数量: %d",
		task.Name, task.Status, task.Progress, len(task.HostIDs))

	st.showInfo("任务详情", details)
}

// deleteTask 删除任务
func (st *ScanTab) deleteTask(task *data.ScanTask) {
	err := st.taskService.DeleteTask(task.ID)
	if err != nil {
		st.showError("删除任务失败", err)
		return
	}

	st.refreshTasks()
	st.selectedTask = nil
	st.toolbar.SetSelectedTask(nil)
	st.updateStatus("任务已删除")
}

// ========== 辅助方法 ==========

// showError 显示错误对话框
func (st *ScanTab) showError(title string, err error) {
	dialog.ShowError(err, st.window)
	st.updateStatus(fmt.Sprintf("错误: %s", err.Error()))
}

// showInfo 显示信息对话框
func (st *ScanTab) showInfo(title, message string) {
	dialog.ShowInformation(title, message, st.window)
}
