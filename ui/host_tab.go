package ui

import (
	"bytes"
	"fmt"
	"io"
	"strings"
	"unicode/utf8"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/storage"
	"fyne.io/fyne/v2/widget"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"

	"lcheck/data"
	"lcheck/services"
	"lcheck/ui/components"
)

// HostTab 主机管理选项卡 - 模块化门面，委托给专门的UI组件
type HostTab struct {
	// 服务层依赖
	hostService   *services.HostService
	exportService *services.ExportService

	// 基础依赖
	updateStatus func(string)
	window       fyne.Window
	app          *App

	// UI组件 - 使用专门的组件，遵循模块化规范
	content    *fyne.Container
	toolbar    *components.HostToolbar
	hostTable  *components.HostTable
	groupList  *components.GroupList
	operations *components.ItemOperations // 通用操作管理器

	// 数据
	hosts         []data.HostInfo
	groups        []data.HostGroup
	selectedHost  *data.HostInfo
	selectedGroup *data.HostGroup
}

// NewHostTab 创建主机管理选项卡 - 使用模块化组件
func NewHostTab(storage data.Storage, updateStatus func(string)) *HostTab {
	tab := &HostTab{
		hostService:   nil, // 延迟初始化，等待SSH客户端注入
		exportService: services.NewExportService(storage),
		updateStatus:  updateStatus,
		hosts:         make([]data.HostInfo, 0),
		groups:        make([]data.HostGroup, 0),
		operations:    components.NewItemOperations(nil), // 暂时传nil，后续设置window
	}

	tab.buildUI()
	tab.setupCallbacks()
	tab.loadData()

	return tab
}

// SetWindow 设置窗口引用
// 遵循模块化规范：延迟设置依赖
func (ht *HostTab) SetWindow(window fyne.Window) {
	ht.window = window
	ht.operations = components.NewItemOperations(window) // 重新初始化操作管理器
}

// SetApp 设置应用引用
func (ht *HostTab) SetApp(app *App) {
	ht.app = app

	// 初始化HostService，现在可以获取SSH客户端了
	if ht.hostService == nil && app != nil {
		scanner := app.GetScanner()
		if scanner != nil {
			sshClient := scanner.GetSSHClient()
			ht.hostService = services.NewHostService(app.GetStorage(), sshClient)

			// 服务初始化完成后，重新加载数据
			ht.loadData()
		}
	}
}

// buildUI 构建UI - 使用专门的组件，遵循模块化规范
func (ht *HostTab) buildUI() {
	// 创建专门的UI组件，遵循模块化规范：使用统一的组件
	ht.toolbar = components.NewHostToolbar()
	ht.hostTable = components.NewHostTable()
	ht.groupList = components.NewGroupList()

	// 设置统一的列表尺寸，遵循模块化规范：使用UI常量避免硬编码
	listSize := components.GetListSize()
	ht.hostTable.GetContainer().Resize(listSize)
	ht.groupList.SetSize(listSize)

	// 创建左右分割布局，遵循模块化规范：使用统一的组件接口
	leftPanel := container.NewBorder(
		widget.NewLabel("主机列表"), // 顶部标签
		nil,                     // 底部
		nil, nil,                // 左右
		ht.hostTable.GetContainer(), // 中心：主机列表
	)

	rightPanel := container.NewBorder(
		widget.NewLabel("主机组"), // 顶部标签
		nil,                    // 底部
		nil, nil,               // 左右
		ht.groupList.GetContainer(), // 中心：主机组列表
	)

	split := container.NewHSplit(leftPanel, rightPanel)
	split.SetOffset(components.HSplitOffset) // 使用常量配置

	// 创建主布局
	ht.content = container.NewBorder(
		ht.toolbar.GetContainer(), // 顶部工具栏
		nil,                       // 底部为空
		nil, nil,                  // 左右为空
		split, // 中间分割面板
	)
}

// setupCallbacks 设置回调函数 - 连接组件和业务逻辑
func (ht *HostTab) setupCallbacks() {
	// 工具栏回调
	ht.toolbar.SetOnAddHost(ht.handleAddHost)
	ht.toolbar.SetOnAddGroup(ht.handleAddGroup)
	ht.toolbar.SetOnEditItem(ht.handleEditItem)
	ht.toolbar.SetOnDeleteItem(ht.handleDeleteItem)
	ht.toolbar.SetOnTestConnection(ht.handleTestConnection)
	ht.toolbar.SetOnImportData(ht.handleImportData)
	ht.toolbar.SetOnExportData(ht.handleExportData)
	ht.toolbar.SetOnRefreshData(ht.handleRefreshData)

	// 主机表格回调，遵循模块化规范：使用统一的事件处理接口
	ht.hostTable.SetOnSelection(ht.handleHostSelection)
	ht.hostTable.SetOnDoubleClick(ht.handleHostDoubleClick)

	// 主机组列表回调，遵循模块化规范：使用统一的事件处理接口
	ht.groupList.SetOnSelection(ht.handleGroupSelection)
	ht.groupList.SetOnDoubleClick(ht.handleGroupDoubleClick)
}

// GetContent 获取内容容器
func (ht *HostTab) GetContent() *fyne.Container {
	return ht.content
}

// ========== 数据操作方法 ==========

// loadData 加载数据 - 委托给服务层
func (ht *HostTab) loadData() {
	hostCount, groupCount := ht.loadHostsAndGroups()
	// 显示完整的加载状态
	ht.updateStatus(fmt.Sprintf("已加载 %d 个主机，%d 个主机组", hostCount, groupCount))
}

// loadHostsAndGroups 加载主机和主机组，返回数量
func (ht *HostTab) loadHostsAndGroups() (int, int) {
	hostCount := ht.loadHosts()
	groupCount := ht.loadGroups()
	return hostCount, groupCount
}

// loadHosts 加载主机列表，返回主机数量
func (ht *HostTab) loadHosts() int {
	if ht.hostService == nil {
		return 0
	}

	hosts, err := ht.hostService.LoadHosts()
	if err != nil {
		ht.showError("加载主机失败", err)
		return 0
	}

	ht.hosts = hosts
	ht.hostTable.SetHosts(hosts)
	return len(hosts)
}

// loadGroups 加载主机组列表，返回主机组数量
func (ht *HostTab) loadGroups() int {
	if ht.hostService == nil {
		return 0
	}

	groups, err := ht.hostService.LoadGroups()
	if err != nil {
		ht.showError("加载主机组失败", err)
		return 0
	}

	ht.groups = groups
	ht.groupList.SetGroups(groups) // 使用组件接口更新数据
	return len(groups)
}

// refreshData 刷新数据
func (ht *HostTab) refreshData() {
	ht.loadData()
}

// RefreshData 刷新数据（公共方法）
func (ht *HostTab) RefreshData() {
	ht.refreshData()
}

// ========== 事件处理方法 ==========

// handleAddHost 处理添加主机
// 遵循模块化规范：使用可复用的操作组件
func (ht *HostTab) handleAddHost() {
	ht.operations.AddHost(ht.saveHost)
}

// handleAddGroup 处理添加主机组
// 遵循模块化规范：使用可复用的操作组件
func (ht *HostTab) handleAddGroup() {
	ht.operations.AddGroup(ht.saveGroup)
}

// handleEditItem 处理编辑项目
// 遵循模块化规范：使用可复用的操作组件
func (ht *HostTab) handleEditItem() {
	if ht.selectedHost != nil {
		ht.operations.EditHost(ht.selectedHost, ht.updateHost)
	} else if ht.selectedGroup != nil {
		ht.operations.EditGroup(ht.selectedGroup, ht.updateGroup)
	} else {
		ht.operations.ShowItemError("编辑失败", fmt.Errorf("请先选择要编辑的主机或主机组"))
		ht.updateStatus("请先选择要编辑的主机或主机组")
	}
}

// handleDeleteItem 处理删除项目
// 遵循模块化规范：使用可复用的操作组件
func (ht *HostTab) handleDeleteItem() {
	if ht.selectedHost != nil {
		ht.operations.DeleteHost(ht.selectedHost, ht.deleteSelectedHost)
	} else if ht.selectedGroup != nil {
		ht.operations.DeleteGroup(ht.selectedGroup, ht.deleteSelectedGroup)
	} else {
		ht.operations.ShowItemError("删除失败", fmt.Errorf("请先选择要删除的主机或主机组"))
		ht.updateStatus("请先选择要删除的主机或主机组")
	}
}

// handleTestConnection 处理测试连接
// 遵循模块化规范：使用可复用的操作组件，支持主机和主机组测试
func (ht *HostTab) handleTestConnection() {
	if ht.selectedHost != nil {
		// 单个主机测试，显示简单对话框
		ht.operations.TestHostConnection(ht.selectedHost, ht.testConnection)
	} else if ht.selectedGroup != nil {
		// 主机组测试，显示详细结果对话框
		ht.operations.TestGroupConnections(ht.selectedGroup, ht.testConnectionForGroup)
	} else {
		ht.operations.ShowItemError("测试失败", fmt.Errorf("请先选择要测试的主机或主机组"))
		ht.updateStatus("请先选择要测试的主机或主机组")
	}
}

// handleImportData 处理导入数据
func (ht *HostTab) handleImportData() {
	ht.showImportDialog()
}

// handleExportData 处理导出数据
func (ht *HostTab) handleExportData() {
	// 检查是否有选中的项目
	if ht.selectedHost != nil {
		// 导出选中的主机
		ht.exportSelectedHost()
	} else if ht.selectedGroup != nil {
		// 导出选中的主机组
		ht.exportSelectedGroup()
	} else {
		// 没有选中任何项目
		components.ShowErrorDialog("导出失败", fmt.Errorf("请先选择要导出的主机或主机组"), ht.window)
		ht.updateStatus("请先选择要导出的主机或主机组")
	}
}

// handleRefreshData 处理刷新数据
func (ht *HostTab) handleRefreshData() {
	ht.refreshData()
}

// handleHostSelection 处理主机选择
// 遵循模块化规范：统一的状态管理，确保选择状态互斥
func (ht *HostTab) handleHostSelection(host *data.HostInfo) {
	// 如果选择了主机，清除主机组选择状态
	if host != nil {
		ht.selectedGroup = nil
		ht.groupList.ClearSelection()
	}

	ht.selectedHost = host

	// 更新工具栏状态
	ht.toolbar.SetSelectedItems(host, nil)

	if host != nil {
		ht.updateStatus(fmt.Sprintf("已选择主机: %s (%s:%s)", host.Name, host.Host, host.Port))
	} else {
		ht.updateStatus("未选择主机")
	}
}

// handleHostDoubleClick 处理主机双击
func (ht *HostTab) handleHostDoubleClick(host *data.HostInfo) {
	if host != nil {
		ht.showHostDetails(host)
	}
}

// handleGroupSelection 处理主机组选择
// 遵循模块化规范：统一的状态管理，确保选择状态互斥
func (ht *HostTab) handleGroupSelection(group *data.HostGroup) {
	// 如果选择了主机组，清除主机选择状态
	if group != nil {
		ht.selectedHost = nil
		ht.hostTable.ClearSelection()
	}

	ht.selectedGroup = group

	// 更新工具栏状态
	ht.toolbar.SetSelectedItems(nil, group)

	if group != nil {
		ht.updateStatus(fmt.Sprintf("已选择主机组: %s (%d台主机)", group.Name, len(group.Hosts)))
	} else {
		ht.updateStatus("未选择主机组")
	}
}

// handleGroupDoubleClick 处理主机组双击
// 遵循模块化规范：统一的事件处理模式
func (ht *HostTab) handleGroupDoubleClick(group *data.HostGroup) {
	if group != nil {
		ht.showGroupDetails(group)
	}
}

// ========== 业务逻辑方法 ==========

// testConnection 测试连接
// 遵循模块化规范：委托给服务层处理业务逻辑，添加进度提示
func (ht *HostTab) testConnection(host *data.HostInfo) {
	ht.updateStatus(fmt.Sprintf("正在测试连接到 %s (%s:%s)...", host.Name, host.Host, host.Port))

	// 检查服务是否已初始化
	if ht.hostService == nil {
		ht.updateStatus("服务未初始化，无法测试连接")
		components.ShowErrorDialog("连接测试失败", fmt.Errorf("服务未初始化，请稍后重试"), ht.window)
		return
	}

	// 创建单个主机测试的进度对话框
	progressTitle := fmt.Sprintf("正在测试主机 '%s' 的连接", host.Name)
	progressData := components.ShowSingleHostProgressDialog(progressTitle, host.Name, ht.window)
	progressData.Dialog.Show()

	// 异步执行测试
	go func() {
		// 使用defer来捕获可能的panic
		defer func() {
			if r := recover(); r != nil {
				progressData.Dialog.Hide()
				ht.updateStatus(fmt.Sprintf("连接测试失败: %v", r))
				components.ShowErrorDialog("连接测试失败", fmt.Errorf("%v", r), ht.window)
			}
		}()

		// 委托给服务层测试连接
		result := ht.hostService.TestConnection(*host)

		// 关闭进度对话框
		progressData.Dialog.Hide()

		// 根据测试结果显示相应的消息
		if result.Status == "连接成功" {
			successMsg := fmt.Sprintf("主机 %s 连接测试成功\n耗时: %v", host.Name, result.Duration)
			ht.updateStatus(successMsg)
			components.ShowSuccessDialog("连接测试成功", successMsg, ht.window)
		} else {
			errorMsg := fmt.Sprintf("主机 %s 连接测试失败\n状态: %s\n错误: %s\n耗时: %v",
				host.Name, result.Status, result.Error, result.Duration)
			ht.updateStatus(fmt.Sprintf("主机 %s 连接测试失败: %s", host.Name, result.Status))
			components.ShowErrorDialog("连接测试失败", fmt.Errorf(errorMsg), ht.window)
		}
	}()
}

// testConnectionForGroup 为主机组测试连接（返回结果而不显示对话框）
// 遵循模块化规范：专门为批量测试设计的方法
func (ht *HostTab) testConnectionForGroup(host *data.HostInfo) *components.ConnectionTestResult {
	// 检查服务是否已初始化
	if ht.hostService == nil {
		return &components.ConnectionTestResult{
			Status:   "连接失败",
			Error:    "服务未初始化",
			Duration: 0,
		}
	}

	// 委托给服务层测试连接
	result := ht.hostService.TestConnection(*host)

	// 转换为组件需要的结果格式
	return &components.ConnectionTestResult{
		Status:   result.Status,
		Error:    result.Error,
		Duration: result.Duration,
	}
}

// ========== 对话框方法 ==========

// showAddHostDialog 显示添加主机对话框
func (ht *HostTab) showAddHostDialog() {
	ht.updateStatus("正在打开添加主机对话框...")

	components.CreateHostFormDialog("添加主机", nil, func(name, hostAddr, username, password string, port int) {
		// 检查服务是否已初始化
		if ht.hostService == nil {
			ht.updateStatus("服务未初始化，无法创建主机")
			components.ShowErrorDialog("创建失败", fmt.Errorf("服务未初始化，请稍后重试"), ht.window)
			return
		}

		// 创建主机
		host, err := ht.hostService.CreateHost(name, hostAddr, username, password, port)
		if err != nil {
			ht.updateStatus(fmt.Sprintf("创建主机失败: %v", err))
			components.ShowErrorDialog("创建失败", err, ht.window)
			return
		}

		// 刷新数据
		ht.refreshData()
		ht.updateStatus(fmt.Sprintf("主机 %s 已添加", host.Name))
		components.ShowSuccessDialog("添加成功", fmt.Sprintf("主机 %s 已成功添加", host.Name), ht.window)
	}, ht.window)
}

// showComplexAddHostDialog 显示复杂的添加主机对话框
func (ht *HostTab) showComplexAddHostDialog(nameEntry, hostEntry, portEntry, usernameEntry *widget.Entry, passwordEntry *widget.Entry, descEntry *widget.Entry) {
	ht.updateStatus("创建复杂对话框...")

	// 创建表单
	form := container.NewVBox(
		widget.NewLabel("添加新主机"),
		widget.NewSeparator(),
		widget.NewForm(
			widget.NewFormItem("主机名称", nameEntry),
			widget.NewFormItem("主机地址", hostEntry),
			widget.NewFormItem("端口", portEntry),
			widget.NewFormItem("用户名", usernameEntry),
			widget.NewFormItem("密码", passwordEntry),
			widget.NewFormItem("描述", descEntry),
		),
	)

	// 创建对话框
	dialog := dialog.NewCustomConfirm("添加主机", "添加", "取消", form, func(confirmed bool) {
		if !confirmed {
			return
		}

		// 验证输入
		if nameEntry.Text == "" || hostEntry.Text == "" || usernameEntry.Text == "" {
			ht.showError("输入错误", fmt.Errorf("主机名称、地址和用户名不能为空"))
			return
		}

		// 解析端口
		port := 22
		if portEntry.Text != "" {
			if p, err := fmt.Sscanf(portEntry.Text, "%d", &port); err != nil || p != 1 {
				ht.showError("输入错误", fmt.Errorf("端口必须是数字"))
				return
			}
		}

		// 创建主机
		host, err := ht.hostService.CreateHost(
			nameEntry.Text,
			hostEntry.Text,
			usernameEntry.Text,
			passwordEntry.Text,
			port,
		)
		if err != nil {
			ht.showError("创建主机失败", err)
			return
		}

		// 刷新数据
		ht.refreshData()
		ht.updateStatus(fmt.Sprintf("主机 %s 已添加", host.Name))
	}, ht.window)

	dialog.Resize(fyne.NewSize(400, 500))
	dialog.Show()
}

// showAddGroupDialog 显示添加主机组对话框
func (ht *HostTab) showAddGroupDialog() {
	ht.updateStatus("正在打开添加主机组对话框...")

	components.ShowAddGroupWithHostsDialog(func(groupName, groupDesc string, hosts []components.GroupHostInfo) {
		// 转换UI主机信息为数据模型
		var groupHosts []data.HostInfo
		for _, hostInfo := range hosts {
			host := data.HostInfo{
				Name:     hostInfo.NameEntry.Text,
				Host:     hostInfo.AddrEntry.Text,
				Port:     "22", // 默认端口
				Username: hostInfo.UserEntry.Text,
				Password: hostInfo.PassEntry.Text,
			}
			groupHosts = append(groupHosts, host)
		}

		// 创建主机组
		group, err := ht.hostService.CreateGroup(groupName, groupDesc, groupHosts)
		if err != nil {
			ht.updateStatus(fmt.Sprintf("创建主机组失败: %v", err))
			components.ShowErrorDialog("创建主机组失败", err, ht.window)
			return
		}

		// 刷新数据
		ht.refreshData()
		ht.updateStatus(fmt.Sprintf("主机组 %s 已创建，包含 %d 个主机", group.Name, len(groupHosts)))
		components.ShowSuccessDialog("创建成功",
			fmt.Sprintf("主机组 %s 已成功创建，包含 %d 个主机", group.Name, len(groupHosts)),
			ht.window)
	}, ht.window)
}

// showEditHostDialog 显示编辑主机对话框
func (ht *HostTab) showEditHostDialog(host *data.HostInfo) {
	ht.updateStatus(fmt.Sprintf("正在打开编辑主机 %s 对话框...", host.Name))

	components.CreateHostFormDialog("编辑主机", host, func(name, hostAddr, username, password string, port int) {
		// 更新主机
		err := ht.hostService.UpdateHost(host.ID, name, hostAddr, username, password, port)
		if err != nil {
			ht.updateStatus(fmt.Sprintf("更新主机失败: %v", err))
			components.ShowErrorDialog("更新失败", err, ht.window)
			return
		}

		// 刷新数据
		ht.refreshData()
		ht.updateStatus(fmt.Sprintf("主机 %s 已更新", host.Name))
		components.ShowSuccessDialog("更新成功", fmt.Sprintf("主机 %s 已成功更新", host.Name), ht.window)
	}, ht.window)
}

// showEditGroupDialog 显示编辑主机组对话框
func (ht *HostTab) showEditGroupDialog(group *data.HostGroup) {
	ht.updateStatus(fmt.Sprintf("点击了编辑主机组 %s 按钮 - 功能正在开发中", group.Name))
}

// showDeleteHostConfirmDialog 显示删除主机确认对话框
func (ht *HostTab) showDeleteHostConfirmDialog() {
	if ht.selectedHost == nil {
		ht.updateStatus("错误: 没有选择要删除的主机")
		return
	}

	ht.updateStatus(fmt.Sprintf("确认删除主机 %s", ht.selectedHost.Name))

	message := fmt.Sprintf("确定要删除主机 '%s' 吗？\n\n主机地址: %s:%s\n用户名: %s\n\n此操作不可撤销！",
		ht.selectedHost.Name, ht.selectedHost.Host, ht.selectedHost.Port, ht.selectedHost.Username)

	components.ShowConfirmDialog("确认删除", message, func() {
		ht.deleteHost(ht.selectedHost)
	}, ht.window)
}

// showDeleteGroupConfirmDialog 显示删除主机组确认对话框
func (ht *HostTab) showDeleteGroupConfirmDialog() {
	ht.updateStatus(fmt.Sprintf("点击了删除主机组 %s 按钮 - 功能正在开发中", ht.selectedGroup.Name))
}

// showHostDetails 显示主机详情
func (ht *HostTab) showHostDetails(host *data.HostInfo) {
	details := fmt.Sprintf("主机详情 - 名称: %s, 地址: %s:%s, 用户名: %s",
		host.Name, host.Host, host.Port, host.Username)
	ht.updateStatus(details)
}

// showGroupDetails 显示主机组详情
// 遵循模块化规范：统一的详情显示模式
func (ht *HostTab) showGroupDetails(group *data.HostGroup) {
	details := fmt.Sprintf("主机组详情 - 名称: %s, 描述: %s, 主机数量: %d",
		group.Name, group.Description, len(group.Hosts))
	ht.updateStatus(details)
}

// showImportDialog 显示导入对话框
// 遵循模块化规范：使用统一的对话框组件
func (ht *HostTab) showImportDialog() {
	ht.updateStatus("正在打开导入对话框...")

	// 创建文件选择对话框
	fileDialog := dialog.NewFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil {
			components.ShowErrorDialog("文件选择失败", err, ht.window)
			return
		}
		if reader == nil {
			ht.updateStatus("取消导入")
			return
		}
		defer reader.Close()

		// 委托给服务层处理导入
		ht.importHostsFromFile(reader)
	}, ht.window)

	// 设置文件过滤器，只显示CSV文件
	fileDialog.SetFilter(storage.NewExtensionFileFilter([]string{".csv"}))
	fileDialog.Show()
}

// importHostsFromFile 从文件导入主机数据
// 遵循模块化规范：委托给服务层处理业务逻辑
func (ht *HostTab) importHostsFromFile(reader fyne.URIReadCloser) {
	ht.updateStatus("正在读取导入文件...")

	// 读取文件内容
	content, err := io.ReadAll(reader)
	if err != nil {
		components.ShowErrorDialog("读取文件失败", err, ht.window)
		ht.updateStatus("文件读取失败")
		return
	}

	// 尝试检测和转换编码
	content, err = ht.detectAndConvertEncoding(content)
	if err != nil {
		components.ShowErrorDialog("文件编码转换失败", err, ht.window)
		ht.updateStatus("文件编码转换失败")
		return
	}

	// 检查文件内容是否为主机格式还是主机组格式
	csvContent := string(content)

	// 获取第一行（标题行）并清理空格
	lines := strings.Split(csvContent, "\n")
	if len(lines) == 0 {
		components.ShowErrorDialog("文件格式错误", fmt.Errorf("CSV文件为空"), ht.window)
		ht.updateStatus("CSV文件为空")
		return
	}

	// 清理标题行的空格和特殊字符
	headerLine := strings.TrimSpace(lines[0])
	headerLine = strings.ReplaceAll(headerLine, "\r", "") // 移除Windows行尾符

	// 检查格式
	hostHeader := "主机名,主机地址,端口,用户名,密码"
	groupHeader := "主机组名,主机组描述,主机名,主机地址,端口,用户名,密码"

	if headerLine == hostHeader {
		// 导入主机
		ht.importHosts(csvContent)
	} else if headerLine == groupHeader {
		// 导入主机组
		ht.importGroups(csvContent)
	} else {
		components.ShowErrorDialog("文件格式错误",
			fmt.Errorf("不支持的CSV格式\n\n当前标题行：%s\n\n支持的格式：\n1. 主机格式：%s\n2. 主机组格式：%s",
				headerLine, hostHeader, groupHeader),
			ht.window)
		ht.updateStatus("不支持的文件格式")
	}
}

// importHosts 导入主机数据
func (ht *HostTab) importHosts(csvContent string) {
	ht.updateStatus("正在解析主机数据...")

	// 使用导出服务解析CSV
	hosts, err := ht.exportService.ImportHostsFromCSV(csvContent)
	if err != nil {
		components.ShowErrorDialog("解析CSV失败", err, ht.window)
		ht.updateStatus("CSV解析失败")
		return
	}

	if len(hosts) == 0 {
		components.ShowErrorDialog("导入失败", fmt.Errorf("CSV文件中没有有效的主机数据"), ht.window)
		ht.updateStatus("没有有效的主机数据")
		return
	}

	// 确认导入
	message := fmt.Sprintf("将要导入 %d 台主机，是否继续？\n\n导入的主机：\n", len(hosts))
	for i, host := range hosts {
		if i < 5 { // 只显示前5台主机
			message += fmt.Sprintf("- %s (%s)\n", host.Name, host.Host)
		} else if i == 5 {
			message += fmt.Sprintf("- ... 还有 %d 台主机\n", len(hosts)-5)
			break
		}
	}

	components.ShowConfirmDialog("确认导入主机", message, func() {
		ht.doImportHosts(hosts)
	}, ht.window)
}

// doImportHosts 执行主机导入
func (ht *HostTab) doImportHosts(hosts []data.HostInfo) {
	ht.updateStatus("正在导入主机...")

	// 使用主机服务导入
	successCount, errors := ht.hostService.ImportHosts(hosts)
	totalCount := len(hosts)
	failureCount := len(errors)

	// 刷新数据
	ht.refreshData()

	// 显示详细结果
	if failureCount > 0 {
		// 有失败的情况
		resultMsg := fmt.Sprintf("导入完成！\n\n📊 统计：成功 %d 个，失败 %d 个\n\n", successCount, failureCount)

		// 只显示前3个错误，更简洁
		resultMsg += "主要问题：\n"
		for i, err := range errors {
			if i < 3 {
				resultMsg += fmt.Sprintf("• %v\n", err)
			} else {
				resultMsg += fmt.Sprintf("• ... 还有 %d 个问题\n", len(errors)-3)
				break
			}
		}

		if successCount > 0 {
			components.ShowErrorDialog("导入部分成功", fmt.Errorf(resultMsg), ht.window)
		} else {
			components.ShowErrorDialog("导入失败", fmt.Errorf(resultMsg), ht.window)
		}
	} else {
		// 全部成功
		resultMsg := fmt.Sprintf("🎉 导入成功！\n\n成功导入 %d 个主机", totalCount)
		components.ShowSuccessDialog("导入成功", resultMsg, ht.window)
	}

	ht.updateStatus(fmt.Sprintf("主机导入完成：成功 %d 个，失败 %d 个", successCount, failureCount))
}

// importGroups 导入主机组数据
func (ht *HostTab) importGroups(csvContent string) {
	ht.updateStatus("正在解析主机组数据...")

	// 使用导出服务解析CSV
	groups, err := ht.exportService.ImportGroupsFromCSV(csvContent)
	if err != nil {
		components.ShowErrorDialog("解析CSV失败", err, ht.window)
		ht.updateStatus("CSV解析失败")
		return
	}

	if len(groups) == 0 {
		components.ShowErrorDialog("导入失败", fmt.Errorf("CSV文件中没有有效的主机组数据"), ht.window)
		ht.updateStatus("没有有效的主机组数据")
		return
	}

	// 确认导入
	message := fmt.Sprintf("将要导入 %d 个主机组，是否继续？\n\n导入的主机组：\n", len(groups))
	for i, group := range groups {
		if i < 5 { // 只显示前5个主机组
			message += fmt.Sprintf("- %s\n", group.Name)
		} else if i == 5 {
			message += fmt.Sprintf("- ... 还有 %d 个主机组\n", len(groups)-5)
			break
		}
	}

	components.ShowConfirmDialog("确认导入主机组", message, func() {
		ht.doImportGroups(groups)
	}, ht.window)
}

// doImportGroups 执行主机组导入（包含完整主机信息）
func (ht *HostTab) doImportGroups(groups []data.HostGroup) {
	ht.updateStatus("正在导入主机组...")

	// 现在主机组包含完整的主机信息，不需要额外处理
	// 直接使用主机服务导入
	successCount, errors := ht.hostService.ImportGroups(groups)
	totalCount := len(groups)
	failureCount := len(errors)

	// 刷新数据
	ht.refreshData()

	// 计算总的主机数量
	totalHosts := 0
	successHosts := 0
	for i, group := range groups {
		totalHosts += len(group.Hosts)
		if i < successCount { // 成功导入的主机组
			successHosts += len(group.Hosts)
		}
	}

	// 显示详细结果
	if failureCount > 0 {
		// 有失败的情况
		resultMsg := fmt.Sprintf("导入完成！\n\n📊 统计：成功 %d 个主机组，失败 %d 个\n\n", successCount, failureCount)

		// 只显示前3个错误，更简洁
		resultMsg += "主要问题：\n"
		for i, err := range errors {
			if i < 3 {
				resultMsg += fmt.Sprintf("• %v\n", err)
			} else {
				resultMsg += fmt.Sprintf("• ... 还有 %d 个问题\n", len(errors)-3)
				break
			}
		}

		if successCount > 0 {
			components.ShowErrorDialog("导入部分成功", fmt.Errorf(resultMsg), ht.window)
		} else {
			components.ShowErrorDialog("导入失败", fmt.Errorf(resultMsg), ht.window)
		}
	} else {
		// 全部成功
		resultMsg := fmt.Sprintf("🎉 导入成功！\n\n成功导入 %d 个主机组（%d 台主机）", totalCount, totalHosts)
		components.ShowSuccessDialog("导入成功", resultMsg, ht.window)
	}

	ht.updateStatus(fmt.Sprintf("主机组导入完成：成功 %d 个，失败 %d 个", successCount, failureCount))
}

// exportSelectedHost 导出选中的主机
func (ht *HostTab) exportSelectedHost() {
	if ht.selectedHost == nil {
		return
	}

	ht.updateStatus("正在导出选中的主机...")

	// 创建文件保存对话框
	fileDialog := dialog.NewFileSave(func(writer fyne.URIWriteCloser, err error) {
		if err != nil {
			components.ShowErrorDialog("文件保存失败", err, ht.window)
			return
		}
		if writer == nil {
			ht.updateStatus("取消导出")
			return
		}
		defer writer.Close()

		// 导出选中的主机
		hosts := []data.HostInfo{*ht.selectedHost}
		csvContent := ht.exportService.ExportHostsToCSV(hosts)

		// 写入文件
		if _, err := writer.Write([]byte(csvContent)); err != nil {
			components.ShowErrorDialog("写入文件失败", err, ht.window)
			return
		}

		// 显示成功消息
		successMsg := fmt.Sprintf("主机 '%s' 已成功导出到 CSV 文件", ht.selectedHost.Name)
		components.ShowSuccessDialog("导出成功", successMsg, ht.window)
		ht.updateStatus("主机导出完成")
	}, ht.window)

	// 设置默认文件名
	fileName := fmt.Sprintf("host_%s.csv", ht.selectedHost.Name)
	fileDialog.SetFileName(fileName)
	fileDialog.Show()
}

// exportSelectedGroup 导出选中的主机组
func (ht *HostTab) exportSelectedGroup() {
	if ht.selectedGroup == nil {
		return
	}

	ht.updateStatus("正在导出选中的主机组...")

	// 创建文件保存对话框
	fileDialog := dialog.NewFileSave(func(writer fyne.URIWriteCloser, err error) {
		if err != nil {
			components.ShowErrorDialog("文件保存失败", err, ht.window)
			return
		}
		if writer == nil {
			ht.updateStatus("取消导出")
			return
		}
		defer writer.Close()

		// 导出选中的主机组
		groups := []data.HostGroup{*ht.selectedGroup}
		csvContent := ht.exportService.ExportGroupsToCSV(groups)

		// 写入文件
		if _, err := writer.Write([]byte(csvContent)); err != nil {
			components.ShowErrorDialog("写入文件失败", err, ht.window)
			return
		}

		// 显示成功消息
		successMsg := fmt.Sprintf("主机组 '%s' 已成功导出到 CSV 文件\n包含 %d 台主机",
			ht.selectedGroup.Name, len(ht.selectedGroup.Hosts))
		components.ShowSuccessDialog("导出成功", successMsg, ht.window)
		ht.updateStatus("主机组导出完成")
	}, ht.window)

	// 设置默认文件名
	fileName := fmt.Sprintf("group_%s.csv", ht.selectedGroup.Name)
	fileDialog.SetFileName(fileName)
	fileDialog.Show()
}

// deleteHost 删除主机
func (ht *HostTab) deleteHost(host *data.HostInfo) {
	err := ht.hostService.DeleteHost(host.ID)
	if err != nil {
		ht.showError("删除主机失败", err)
		return
	}

	ht.refreshData()
	ht.selectedHost = nil
	ht.toolbar.SetSelectedItems(nil, nil)
	ht.updateStatus("主机已删除")
}

// deleteGroup 删除主机组
func (ht *HostTab) deleteGroup(group *data.HostGroup) {
	err := ht.hostService.DeleteGroup(group.ID)
	if err != nil {
		ht.showError("删除主机组失败", err)
		return
	}

	ht.refreshData()
	ht.selectedGroup = nil
	ht.toolbar.SetSelectedItems(nil, nil)
	ht.updateStatus("主机组已删除")
}

// ========== 辅助方法 ==========

// checkServiceInitialized 检查服务是否已初始化
func (ht *HostTab) checkServiceInitialized() bool {
	if ht.hostService == nil {
		ht.updateStatus("服务未初始化，请稍后重试")
		components.ShowErrorDialog("服务未初始化",
			fmt.Errorf("主机服务正在初始化中，请稍后重试"), ht.window)
		return false
	}
	return true
}

// showError 显示错误对话框
func (ht *HostTab) showError(title string, err error) {
	ht.updateStatus(fmt.Sprintf("错误 - %s: %s", title, err.Error()))
	components.ShowErrorDialog(title, err, ht.window)
}

// showInfo 显示信息对话框
func (ht *HostTab) showInfo(title, message string) {
	ht.updateStatus(fmt.Sprintf("%s: %s", title, message))
	components.ShowSuccessDialog(title, message, ht.window)
}

// ========== 模块化回调方法 ==========

// saveHost 保存主机 - 模块化回调函数
func (ht *HostTab) saveHost(name, hostAddr, username, password string, port int) {
	ht.updateStatus(fmt.Sprintf("正在保存主机: %s (%s:%d)...", name, hostAddr, port))

	// 检查服务是否已初始化
	if ht.hostService == nil {
		ht.updateStatus("服务未初始化，无法保存主机")
		components.ShowErrorDialog("保存失败", fmt.Errorf("服务未初始化，请稍后重试"), ht.window)
		return
	}

	// 调用服务层保存主机
	host, err := ht.hostService.CreateHost(name, hostAddr, username, password, port)
	if err != nil {
		components.ShowErrorDialog("保存失败", err, ht.window)
		ht.updateStatus(fmt.Sprintf("保存主机失败: %v", err))
		return
	}

	// 添加到本地列表并更新UI
	ht.hosts = append(ht.hosts, *host)
	ht.hostTable.SetHosts(ht.hosts)

	// 显示成功消息
	components.ShowSuccessDialog("保存成功", fmt.Sprintf("主机 '%s' 已成功添加", name), ht.window)
	ht.updateStatus(fmt.Sprintf("主机 '%s' 保存成功", name))
}

// updateHost 更新主机 - 模块化回调函数
func (ht *HostTab) updateHost(name, hostAddr, username, password string, port int) {
	if ht.selectedHost == nil {
		components.ShowErrorDialog("更新失败", fmt.Errorf("没有选择要更新的主机"), ht.window)
		return
	}

	ht.updateStatus(fmt.Sprintf("正在更新主机: %s (%s:%d)...", name, hostAddr, port))

	// 调用服务层更新主机
	err := ht.hostService.UpdateHost(ht.selectedHost.ID, name, hostAddr, username, password, port)
	if err != nil {
		components.ShowErrorDialog("更新失败", err, ht.window)
		ht.updateStatus(fmt.Sprintf("更新主机失败: %v", err))
		return
	}

	// 更新本地列表中的主机信息
	for i, host := range ht.hosts {
		if host.ID == ht.selectedHost.ID {
			ht.hosts[i].Name = name
			ht.hosts[i].Host = hostAddr
			ht.hosts[i].Username = username
			ht.hosts[i].Password = password
			ht.hosts[i].Port = fmt.Sprintf("%d", port)
			break
		}
	}

	// 更新UI
	ht.hostTable.SetHosts(ht.hosts)

	// 显示成功消息
	components.ShowSuccessDialog("更新成功", fmt.Sprintf("主机 '%s' 已成功更新", name), ht.window)
	ht.updateStatus(fmt.Sprintf("主机 '%s' 更新成功", name))
}

// deleteSelectedHost 删除选中的主机 - 模块化回调函数
func (ht *HostTab) deleteSelectedHost() {
	if ht.selectedHost != nil {
		ht.deleteHost(ht.selectedHost)
	}
}

// saveGroup 保存主机组 - 模块化回调函数
func (ht *HostTab) saveGroup(name, description string, hosts []components.GroupHostInfo) {
	ht.updateStatus(fmt.Sprintf("正在保存主机组: %s...", name))

	// 转换UI主机信息为数据模型
	var groupHosts []data.HostInfo
	for _, hostInfo := range hosts {
		host := data.HostInfo{
			Name:     hostInfo.NameEntry.Text,
			Host:     hostInfo.AddrEntry.Text,
			Port:     "22", // 默认端口
			Username: hostInfo.UserEntry.Text,
			Password: hostInfo.PassEntry.Text,
		}
		groupHosts = append(groupHosts, host)
	}

	// 使用主机服务创建主机组，传入主机信息列表
	group, err := ht.hostService.CreateGroup(name, description, groupHosts)
	if err != nil {
		ht.updateStatus(fmt.Sprintf("创建主机组失败: %v", err))
		components.ShowErrorDialog("创建主机组失败", err, ht.window)
		return
	}

	// 刷新数据以显示新创建的主机组
	ht.refreshData()

	// 显示成功消息
	hostCount := len(groupHosts)
	components.ShowSuccessDialog("保存成功",
		fmt.Sprintf("主机组 '%s' 已成功添加\n包含 %d 台主机", group.Name, hostCount), ht.window)
	ht.updateStatus(fmt.Sprintf("主机组 '%s' 保存成功，包含 %d 台主机", group.Name, hostCount))
}

// updateGroup 更新主机组 - 模块化回调函数
func (ht *HostTab) updateGroup(name, description string, hosts []components.GroupHostInfo) {
	if ht.selectedGroup == nil {
		components.ShowErrorDialog("更新失败", fmt.Errorf("没有选择要更新的主机组"), ht.window)
		return
	}

	ht.updateStatus(fmt.Sprintf("正在更新主机组: %s...", name))

	// 转换GroupHostInfo为HostInfo
	var groupHosts []data.HostInfo
	for i, hostInfo := range hosts {
		host := data.HostInfo{
			ID:       fmt.Sprintf("host_%s_%d", name, i+1), // 生成主机ID
			Name:     hostInfo.NameEntry.Text,
			Host:     hostInfo.AddrEntry.Text,
			Username: hostInfo.UserEntry.Text,
			Password: hostInfo.PassEntry.Text,
			Port:     "22", // 默认端口
		}
		groupHosts = append(groupHosts, host)
	}

	// 保存原始数据，以便回滚
	var originalGroup *data.HostGroup
	var groupIndex int = -1

	// 更新本地列表中的主机组信息
	for i, group := range ht.groups {
		if group.ID == ht.selectedGroup.ID {
			originalGroup = &group // 保存原始数据
			groupIndex = i
			ht.groups[i].Name = name
			ht.groups[i].Description = description
			ht.groups[i].Hosts = groupHosts // 更新主机列表
			break
		}
	}

	// 调用服务层持久化保存
	err := ht.hostService.SaveGroups(ht.groups)
	if err != nil {
		// 保存失败，回滚到原始数据
		if originalGroup != nil && groupIndex >= 0 {
			ht.groups[groupIndex] = *originalGroup
		}
		components.ShowErrorDialog("更新失败", err, ht.window)
		ht.updateStatus(fmt.Sprintf("更新主机组失败: %v", err))
		return
	}

	// 更新UI
	ht.groupList.SetGroups(ht.groups)

	// 显示成功消息
	hostCount := len(groupHosts)
	components.ShowSuccessDialog("更新成功",
		fmt.Sprintf("主机组 '%s' 已成功更新\n包含 %d 台主机", name, hostCount), ht.window)
	ht.updateStatus(fmt.Sprintf("主机组 '%s' 更新成功，包含 %d 台主机", name, hostCount))
}

// deleteSelectedGroup 删除选中的主机组 - 模块化回调函数
func (ht *HostTab) deleteSelectedGroup() {
	if ht.selectedGroup != nil {
		ht.deleteGroup(ht.selectedGroup)
	}
}

// detectAndConvertEncoding 检测并转换文件编码
func (ht *HostTab) detectAndConvertEncoding(content []byte) ([]byte, error) {
	// 如果已经是有效的UTF-8，直接返回
	if utf8.Valid(content) {
		return content, nil
	}

	// 尝试从GBK转换为UTF-8
	gbkDecoder := simplifiedchinese.GBK.NewDecoder()
	utf8Content, _, err := transform.Bytes(gbkDecoder, content)
	if err == nil && utf8.Valid(utf8Content) {
		return utf8Content, nil
	}

	// 尝试从GB18030转换为UTF-8
	gb18030Decoder := simplifiedchinese.GB18030.NewDecoder()
	utf8Content, _, err = transform.Bytes(gb18030Decoder, content)
	if err == nil && utf8.Valid(utf8Content) {
		return utf8Content, nil
	}

	// 如果都失败了，尝试移除BOM并返回原内容
	if len(content) >= 3 && bytes.Equal(content[:3], []byte{0xEF, 0xBB, 0xBF}) {
		// UTF-8 BOM
		return content[3:], nil
	}

	if len(content) >= 2 {
		if bytes.Equal(content[:2], []byte{0xFF, 0xFE}) || bytes.Equal(content[:2], []byte{0xFE, 0xFF}) {
			// UTF-16 BOM - 这种情况比较复杂，暂时返回错误
			return nil, fmt.Errorf("检测到UTF-16编码文件，请在WPS/Excel中另存为UTF-8编码的CSV文件")
		}
	}

	// 最后尝试直接使用原内容
	return content, nil
}
