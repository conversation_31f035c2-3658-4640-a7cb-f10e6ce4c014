package ui

import (
	"time"

	"fyne.io/fyne/v2"

	"lcheck/core"
	"lcheck/data"
)

// App 主应用结构 - 简化的应用架构
type App struct {
	// 核心组件
	storage data.Storage
	scanner *core.Scanner

	// UI管理器
	windowManager *WindowManager
	tabManager    *TabManager
}

// NewApp 创建新的应用实例 - 简化的应用架构
func NewApp() *App {
	// 初始化存储
	storage, err := data.NewStorage()
	if err != nil {
		panic("初始化数据库失败: " + err.Error())
	}

	// 使用默认配置创建扫描器
	scanner := core.NewScanner(storage, 30*time.Second, 60*time.Second, 10)

	// 创建应用实例
	app := &App{
		storage: storage,
		scanner: scanner,
	}

	// 初始化应用
	app.initializeManagers()
	app.setupCallbacks()
	app.loadData()

	return app
}

// initializeManagers 初始化管理器 - 使用专门的管理器
func (a *App) initializeManagers() {
	// 创建窗口管理器
	a.windowManager = NewWindowManager()

	// 创建标签页管理器
	a.tabManager = NewTabManager(a.storage, a.scanner, a.updateStatus)

	// 设置窗口内容
	a.windowManager.SetContent(a.tabManager.GetTabs())

	// 传递窗口引用给标签页
	a.tabManager.SetWindow(a.windowManager.GetWindow())
	a.tabManager.SetApp(a)
}

// setupCallbacks 设置回调函数 - 连接管理器和业务逻辑
func (a *App) setupCallbacks() {
	// 设置窗口关闭回调
	a.windowManager.SetOnWindowClose(a.onWindowClose)
}

// loadData 加载数据 - 委托给标签页管理器
func (a *App) loadData() {
	a.tabManager.RefreshAllTabs()
	a.updateStatus("应用初始化完成")
}

// updateStatus 更新状态栏 - 委托给窗口管理器
func (a *App) updateStatus(message string) {
	a.windowManager.UpdateStatus(message)
}

// onWindowClose 窗口关闭处理
func (a *App) onWindowClose() {
	// 关闭数据库连接
	if err := a.storage.Close(); err != nil {
		a.ShowError("关闭数据库失败", err.Error())
	}

	// 关闭应用
	a.windowManager.Close()
}

// ========== 公共接口方法 ==========

// Run 运行应用 - 委托给窗口管理器
func (a *App) Run() {
	a.windowManager.ShowAndRun()
}

// GetWindow 获取窗口 - 委托给窗口管理器
func (a *App) GetWindow() fyne.Window {
	return a.windowManager.GetWindow()
}

// GetStorage 获取存储
func (a *App) GetStorage() data.Storage {
	return a.storage
}

// GetScanner 获取扫描器
func (a *App) GetScanner() *core.Scanner {
	return a.scanner
}

// ========== 数据操作方法 ==========

// RefreshData 刷新数据 - 委托给标签页管理器
func (a *App) RefreshData() {
	a.tabManager.RefreshAllTabs()
}

// RefreshCurrentTab 刷新当前标签页 - 委托给标签页管理器
func (a *App) RefreshCurrentTab() {
	a.tabManager.RefreshCurrentTab()
}

// NotifyDataChanged 通知数据已更改
func (a *App) NotifyDataChanged() {
	a.RefreshData()
}

// RefreshHostData 刷新主机数据
func (a *App) RefreshHostData() {
	if hostTab := a.tabManager.GetHostTab(); hostTab != nil {
		hostTab.RefreshData()
	}
}

// RefreshScanData 刷新扫描数据
func (a *App) RefreshScanData() {
	if scanTab := a.tabManager.GetScanTab(); scanTab != nil {
		scanTab.RefreshData()
	}
}

// ========== 标签页操作方法 ==========

// SwitchToTab 切换到指定选项卡 - 委托给标签页管理器
func (a *App) SwitchToTab(index int) {
	a.tabManager.SwitchToTab(index)
}

// SwitchToTabByName 根据名称切换标签页 - 委托给标签页管理器
func (a *App) SwitchToTabByName(name string) {
	a.tabManager.SwitchToTabByName(name)
}

// GetCurrentTabIndex 获取当前选项卡索引 - 委托给标签页管理器
func (a *App) GetCurrentTabIndex() int {
	return a.tabManager.GetCurrentTabIndex()
}

// GetCurrentTabName 获取当前标签页名称 - 委托给标签页管理器
func (a *App) GetCurrentTabName() string {
	return a.tabManager.GetCurrentTabName()
}

// ========== 对话框方法 ==========

// ShowError 显示错误对话框 - 委托给窗口管理器
func (a *App) ShowError(title, message string) {
	a.windowManager.ShowError(title, message)
	a.updateStatus("错误: " + message)
}

// ShowInfo 显示信息对话框 - 委托给窗口管理器
func (a *App) ShowInfo(title, message string) {
	a.windowManager.ShowInfo(title, message)
}

// ShowConfirm 显示确认对话框 - 委托给窗口管理器
func (a *App) ShowConfirm(title, message string, callback func(bool)) {
	a.windowManager.ShowConfirm(title, message, callback)
}

// ShowCustomDialog 显示自定义对话框 - 委托给窗口管理器
func (a *App) ShowCustomDialog(title string, content fyne.CanvasObject, callback func()) {
	a.windowManager.ShowCustomDialog(title, content, callback)
}

// ShowFileOpen 显示文件打开对话框 - 委托给窗口管理器
func (a *App) ShowFileOpen(callback func(fyne.URIReadCloser, error)) {
	a.windowManager.ShowFileOpen(callback)
}

// ShowFileSave 显示文件保存对话框 - 委托给窗口管理器
func (a *App) ShowFileSave(callback func(fyne.URIWriteCloser, error)) {
	a.windowManager.ShowFileSave(callback)
}

// ShowFolderOpen 显示文件夹打开对话框 - 委托给窗口管理器
func (a *App) ShowFolderOpen(callback func(fyne.ListableURI, error)) {
	a.windowManager.ShowFolderOpen(callback)
}

// ========== 窗口操作方法 ==========

// SetTitle 设置窗口标题 - 委托给窗口管理器
func (a *App) SetTitle(title string) {
	a.windowManager.SetTitle(title)
}

// SetSize 设置窗口大小 - 委托给窗口管理器
func (a *App) SetSize(size fyne.Size) {
	a.windowManager.SetSize(size)
}

// CenterOnScreen 窗口居中显示 - 委托给窗口管理器
func (a *App) CenterOnScreen() {
	a.windowManager.CenterOnScreen()
}

// SetFullScreen 设置全屏 - 委托给窗口管理器
func (a *App) SetFullScreen(fullscreen bool) {
	a.windowManager.SetFullScreen(fullscreen)
}

// IsFullScreen 检查是否全屏 - 委托给窗口管理器
func (a *App) IsFullScreen() bool {
	return a.windowManager.IsFullScreen()
}

// ========== 管理器访问方法 ==========

// GetWindowManager 获取窗口管理器
func (a *App) GetWindowManager() *WindowManager {
	return a.windowManager
}

// GetTabManager 获取标签页管理器
func (a *App) GetTabManager() *TabManager {
	return a.tabManager
}

// GetHostTab 获取主机管理标签页
func (a *App) GetHostTab() *HostTab {
	return a.tabManager.GetHostTab()
}

// GetScanTab 获取扫描标签页
func (a *App) GetScanTab() *ScanTab {
	return a.tabManager.GetScanTab()
}

// ========== 状态管理方法 ==========

// UpdateStatus 更新状态栏消息
func (a *App) UpdateStatus(message string) {
	a.updateStatus(message)
}

// GetStatusText 获取状态文本 - 委托给窗口管理器
func (a *App) GetStatusText() string {
	return a.windowManager.GetStatusText()
}

// ClearStatus 清除状态 - 委托给窗口管理器
func (a *App) ClearStatus() {
	a.windowManager.ClearStatus()
}

// ========== 应用生命周期方法 ==========

// Initialize 初始化应用（如果需要延迟初始化）
func (a *App) Initialize() {
	a.loadData()
}

// Shutdown 关闭应用
func (a *App) Shutdown() {
	a.onWindowClose()
}

// SaveState 保存应用状态
func (a *App) SaveState() error {
	// 简化实现，不再需要保存配置
	return nil
}

// LoadState 加载应用状态
func (a *App) LoadState() error {
	// 简化实现，不再需要加载配置
	return nil
}
