package ui

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// WindowManager 窗口管理器 - 专门负责窗口的创建和管理
type WindowManager struct {
	fyneApp     fyne.App
	window      fyne.Window
	statusBar   *fyne.Container
	statusLabel *widget.Label
	timeLabel   *widget.Label

	// 回调函数
	onWindowClose func()
}

// NewWindowManager 创建窗口管理器
func NewWindowManager() *WindowManager {
	wm := &WindowManager{
		fyneApp: app.New(),
	}

	wm.createWindow()
	wm.createStatusBar()
	wm.setupWindowEvents()

	return wm
}

// createWindow 创建主窗口
func (wm *WindowManager) createWindow() {
	wm.window = wm.fyneApp.NewWindow("Lcheck v3.2.0")
	wm.window.Resize(fyne.NewSize(900, 600))
	wm.window.CenterOnScreen()

	// 设置窗口图标（如果有的话）
	// wm.window.SetIcon(resourceIcon)
}

// createStatusBar 创建状态栏
func (wm *WindowManager) createStatusBar() {
	wm.statusLabel = widget.NewLabel("就绪")
	wm.timeLabel = widget.NewLabel(time.Now().Format("2006-01-02 15:04:05"))

	// 创建状态栏容器
	wm.statusBar = container.NewBorder(
		nil, nil,
		wm.statusLabel,
		wm.timeLabel,
		nil,
	)

	// 启动时间更新
	wm.startTimeUpdater()
}

// setupWindowEvents 设置窗口事件
func (wm *WindowManager) setupWindowEvents() {
	wm.window.SetCloseIntercept(func() {
		if wm.onWindowClose != nil {
			wm.onWindowClose()
		} else {
			wm.window.Close()
		}
	})
}

// startTimeUpdater 启动时间更新器
func (wm *WindowManager) startTimeUpdater() {
	go func() {
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()

		for range ticker.C {
			wm.timeLabel.SetText(time.Now().Format("2006-01-02 15:04:05"))
		}
	}()
}

// GetWindow 获取窗口
func (wm *WindowManager) GetWindow() fyne.Window {
	return wm.window
}

// GetFyneApp 获取Fyne应用
func (wm *WindowManager) GetFyneApp() fyne.App {
	return wm.fyneApp
}

// GetStatusBar 获取状态栏
func (wm *WindowManager) GetStatusBar() *fyne.Container {
	return wm.statusBar
}

// SetContent 设置窗口内容
func (wm *WindowManager) SetContent(content fyne.CanvasObject) {
	// 创建带状态栏的主布局
	mainContent := container.NewBorder(
		nil,          // 顶部
		wm.statusBar, // 底部状态栏
		nil, nil,     // 左右
		content, // 中间内容
	)

	wm.window.SetContent(mainContent)
}

// UpdateStatus 更新状态栏消息
func (wm *WindowManager) UpdateStatus(message string) {
	wm.statusLabel.SetText(message)
}

// SetOnWindowClose 设置窗口关闭回调
func (wm *WindowManager) SetOnWindowClose(callback func()) {
	wm.onWindowClose = callback
}

// ShowAndRun 显示并运行窗口
func (wm *WindowManager) ShowAndRun() {
	wm.window.ShowAndRun()
}

// Close 关闭窗口
func (wm *WindowManager) Close() {
	wm.window.Close()
}

// SetTitle 设置窗口标题
func (wm *WindowManager) SetTitle(title string) {
	wm.window.SetTitle(title)
}

// SetSize 设置窗口大小
func (wm *WindowManager) SetSize(size fyne.Size) {
	wm.window.Resize(size)
}

// SetPosition 设置窗口位置
func (wm *WindowManager) SetPosition(position fyne.Position) {
	// Fyne v2中Window没有Move方法，简化实现
}

// CenterOnScreen 窗口居中显示
func (wm *WindowManager) CenterOnScreen() {
	wm.window.CenterOnScreen()
}

// SetFullScreen 设置全屏
func (wm *WindowManager) SetFullScreen(fullscreen bool) {
	wm.window.SetFullScreen(fullscreen)
}

// IsFullScreen 检查是否全屏
func (wm *WindowManager) IsFullScreen() bool {
	return wm.window.FullScreen()
}

// SetFixedSize 设置固定大小
func (wm *WindowManager) SetFixedSize(fixed bool) {
	wm.window.SetFixedSize(fixed)
}

// ShowError 显示错误对话框
func (wm *WindowManager) ShowError(title, message string) {
	dialog.ShowError(fmt.Errorf(message), wm.window)
}

// ShowInfo 显示信息对话框
func (wm *WindowManager) ShowInfo(title, message string) {
	dialog.ShowInformation(title, message, wm.window)
}

// ShowConfirm 显示确认对话框
func (wm *WindowManager) ShowConfirm(title, message string, callback func(bool)) {
	dialog.ShowConfirm(title, message, callback, wm.window)
}

// ShowCustomDialog 显示自定义对话框
func (wm *WindowManager) ShowCustomDialog(title string, content fyne.CanvasObject, callback func()) {
	dialog := dialog.NewCustom(title, "确定", content, wm.window)
	dialog.SetOnClosed(callback)
	dialog.Show()
}

// ShowFileOpen 显示文件打开对话框
func (wm *WindowManager) ShowFileOpen(callback func(fyne.URIReadCloser, error)) {
	dialog.ShowFileOpen(callback, wm.window)
}

// ShowFileSave 显示文件保存对话框
func (wm *WindowManager) ShowFileSave(callback func(fyne.URIWriteCloser, error)) {
	dialog.ShowFileSave(callback, wm.window)
}

// ShowFolderOpen 显示文件夹打开对话框
func (wm *WindowManager) ShowFolderOpen(callback func(fyne.ListableURI, error)) {
	dialog.ShowFolderOpen(callback, wm.window)
}

// SetIcon 设置窗口图标
func (wm *WindowManager) SetIcon(icon fyne.Resource) {
	wm.window.SetIcon(icon)
}

// SetMaster 设置主窗口
func (wm *WindowManager) SetMaster() {
	wm.window.SetMaster()
}

// RequestFocus 请求焦点
func (wm *WindowManager) RequestFocus() {
	wm.window.RequestFocus()
}

// Hide 隐藏窗口
func (wm *WindowManager) Hide() {
	wm.window.Hide()
}

// Show 显示窗口
func (wm *WindowManager) Show() {
	wm.window.Show()
}

// SetCloseIntercept 设置关闭拦截
func (wm *WindowManager) SetCloseIntercept(callback func()) {
	wm.window.SetCloseIntercept(callback)
}

// Canvas 获取画布
func (wm *WindowManager) Canvas() fyne.Canvas {
	return wm.window.Canvas()
}

// Content 获取内容
func (wm *WindowManager) Content() fyne.CanvasObject {
	return wm.window.Content()
}

// Padded 检查是否有内边距
func (wm *WindowManager) Padded() bool {
	return wm.window.Padded()
}

// SetPadded 设置内边距
func (wm *WindowManager) SetPadded(padded bool) {
	wm.window.SetPadded(padded)
}

// GetStatusText 获取状态文本
func (wm *WindowManager) GetStatusText() string {
	return wm.statusLabel.Text
}

// GetTimeText 获取时间文本
func (wm *WindowManager) GetTimeText() string {
	return wm.timeLabel.Text
}

// SetStatusImportance 设置状态重要性
func (wm *WindowManager) SetStatusImportance(importance widget.Importance) {
	wm.statusLabel.Importance = importance
}

// ClearStatus 清除状态
func (wm *WindowManager) ClearStatus() {
	wm.statusLabel.SetText("就绪")
	wm.statusLabel.Importance = widget.MediumImportance
}
