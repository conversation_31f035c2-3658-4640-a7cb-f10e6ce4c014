package components

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
)

// ScanToolbar 扫描工具栏组件 - 专门负责扫描相关的工具栏按钮
type ScanToolbar struct {
	container     *fyne.Container
	createButton  *widget.Button
	deleteButton  *widget.Button
	startButton   *widget.Button
	stopButton    *widget.Button
	exportButton  *widget.Button
	refreshButton *widget.Button

	// 回调函数
	onCreateTask  func()
	onDeleteTask  func()
	onStartTask   func()
	onStopTask    func()
	onExportTask  func()
	onRefreshData func()
}

// NewScanToolbar 创建扫描工具栏
func NewScanToolbar() *ScanToolbar {
	st := &ScanToolbar{}
	st.buildToolbar()
	return st
}

// buildToolbar 构建工具栏
func (st *ScanToolbar) buildToolbar() {
	// 创建按钮
	st.createButton = widget.NewButtonWithIcon("创建任务", theme.ContentAddIcon(), func() {
		if st.onCreateTask != nil {
			st.onCreateTask()
		}
	})
	st.createButton.Importance = widget.HighImportance

	st.deleteButton = widget.NewButtonWithIcon("删除任务", theme.DeleteIcon(), func() {
		if st.onDeleteTask != nil {
			st.onDeleteTask()
		}
	})
	st.deleteButton.Importance = widget.DangerImportance

	st.startButton = widget.NewButtonWithIcon("启动任务", theme.MediaPlayIcon(), func() {
		if st.onStartTask != nil {
			st.onStartTask()
		}
	})
	st.startButton.Importance = widget.SuccessImportance

	st.stopButton = widget.NewButtonWithIcon("停止任务", theme.MediaStopIcon(), func() {
		if st.onStopTask != nil {
			st.onStopTask()
		}
	})
	st.stopButton.Importance = widget.WarningImportance

	st.exportButton = widget.NewButtonWithIcon("导出报告", theme.DocumentSaveIcon(), func() {
		if st.onExportTask != nil {
			st.onExportTask()
		}
	})

	st.refreshButton = widget.NewButtonWithIcon("刷新", theme.ViewRefreshIcon(), func() {
		if st.onRefreshData != nil {
			st.onRefreshData()
		}
	})

	// 初始状态：只有创建和刷新按钮可用
	st.updateButtonStates(nil)

	// 创建工具栏容器
	st.container = container.NewHBox(
		st.createButton,
		widget.NewSeparator(),
		st.startButton,
		st.stopButton,
		widget.NewSeparator(),
		st.deleteButton,
		st.exportButton,
		widget.NewSeparator(),
		st.refreshButton,
	)
}

// GetContainer 获取容器
func (st *ScanToolbar) GetContainer() *fyne.Container {
	return st.container
}

// UpdateButtonStates 更新按钮状态
func (st *ScanToolbar) updateButtonStates(selectedTask *data.ScanTask) {
	hasSelection := selectedTask != nil

	// 删除按钮：有选择时可用
	st.deleteButton.Enable()
	if !hasSelection {
		st.deleteButton.Disable()
	}

	// 启动按钮：选中任务且状态为"待执行"时可用
	st.startButton.Enable()
	if !hasSelection || selectedTask.Status != "待执行" {
		st.startButton.Disable()
	}

	// 停止按钮：选中任务且状态为"运行中"时可用
	st.stopButton.Enable()
	if !hasSelection || selectedTask.Status != "运行中" {
		st.stopButton.Disable()
	}

	// 导出按钮：选中任务且状态为"已完成"时可用
	st.exportButton.Enable()
	if !hasSelection || selectedTask.Status != "已完成" {
		st.exportButton.Disable()
	}

	// 创建和刷新按钮始终可用
	st.createButton.Enable()
	st.refreshButton.Enable()
}

// SetSelectedTask 设置选中的任务（更新按钮状态）
func (st *ScanToolbar) SetSelectedTask(task *data.ScanTask) {
	st.updateButtonStates(task)
}

// SetOnCreateTask 设置创建任务回调
func (st *ScanToolbar) SetOnCreateTask(callback func()) {
	st.onCreateTask = callback
}

// SetOnDeleteTask 设置删除任务回调
func (st *ScanToolbar) SetOnDeleteTask(callback func()) {
	st.onDeleteTask = callback
}

// SetOnStartTask 设置启动任务回调
func (st *ScanToolbar) SetOnStartTask(callback func()) {
	st.onStartTask = callback
}

// SetOnStopTask 设置停止任务回调
func (st *ScanToolbar) SetOnStopTask(callback func()) {
	st.onStopTask = callback
}

// SetOnExportTask 设置导出任务回调
func (st *ScanToolbar) SetOnExportTask(callback func()) {
	st.onExportTask = callback
}

// SetOnRefreshData 设置刷新数据回调
func (st *ScanToolbar) SetOnRefreshData(callback func()) {
	st.onRefreshData = callback
}

// EnableAllButtons 启用所有按钮
func (st *ScanToolbar) EnableAllButtons() {
	st.createButton.Enable()
	st.deleteButton.Enable()
	st.startButton.Enable()
	st.stopButton.Enable()
	st.exportButton.Enable()
	st.refreshButton.Enable()
}

// DisableAllButtons 禁用所有按钮
func (st *ScanToolbar) DisableAllButtons() {
	st.createButton.Disable()
	st.deleteButton.Disable()
	st.startButton.Disable()
	st.stopButton.Disable()
	st.exportButton.Disable()
	st.refreshButton.Disable()
}

// SetCreateButtonText 设置创建按钮文本
func (st *ScanToolbar) SetCreateButtonText(text string) {
	st.createButton.SetText(text)
}

// SetDeleteButtonText 设置删除按钮文本
func (st *ScanToolbar) SetDeleteButtonText(text string) {
	st.deleteButton.SetText(text)
}

// SetStartButtonText 设置启动按钮文本
func (st *ScanToolbar) SetStartButtonText(text string) {
	st.startButton.SetText(text)
}

// SetStopButtonText 设置停止按钮文本
func (st *ScanToolbar) SetStopButtonText(text string) {
	st.stopButton.SetText(text)
}

// SetExportButtonText 设置导出按钮文本
func (st *ScanToolbar) SetExportButtonText(text string) {
	st.exportButton.SetText(text)
}

// SetRefreshButtonText 设置刷新按钮文本
func (st *ScanToolbar) SetRefreshButtonText(text string) {
	st.refreshButton.SetText(text)
}

// GetCreateButton 获取创建按钮
func (st *ScanToolbar) GetCreateButton() *widget.Button {
	return st.createButton
}

// GetDeleteButton 获取删除按钮
func (st *ScanToolbar) GetDeleteButton() *widget.Button {
	return st.deleteButton
}

// GetStartButton 获取启动按钮
func (st *ScanToolbar) GetStartButton() *widget.Button {
	return st.startButton
}

// GetStopButton 获取停止按钮
func (st *ScanToolbar) GetStopButton() *widget.Button {
	return st.stopButton
}

// GetExportButton 获取导出按钮
func (st *ScanToolbar) GetExportButton() *widget.Button {
	return st.exportButton
}

// GetRefreshButton 获取刷新按钮
func (st *ScanToolbar) GetRefreshButton() *widget.Button {
	return st.refreshButton
}

// ShowProgress 显示进度状态
func (st *ScanToolbar) ShowProgress(inProgress bool) {
	if inProgress {
		st.createButton.Disable()
		st.deleteButton.Disable()
		st.startButton.Disable()
		st.exportButton.Disable()
		st.stopButton.Enable()
	} else {
		st.stopButton.Disable()
		// 其他按钮状态由 updateButtonStates 控制
	}
}

// SetButtonImportance 设置按钮重要性
func (st *ScanToolbar) SetButtonImportance(buttonName string, importance widget.ButtonImportance) {
	switch buttonName {
	case "create":
		st.createButton.Importance = importance
	case "delete":
		st.deleteButton.Importance = importance
	case "start":
		st.startButton.Importance = importance
	case "stop":
		st.stopButton.Importance = importance
	case "export":
		st.exportButton.Importance = importance
	case "refresh":
		st.refreshButton.Importance = importance
	}
}
