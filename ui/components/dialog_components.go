package components

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/storage"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
)

// GroupHostInfo 组内主机信息结构
type GroupHostInfo struct {
	NameEntry *widget.Entry
	AddrEntry *widget.Entry
	UserEntry *widget.Entry
	PassEntry *widget.Entry
	Container *fyne.Container
}

// ShowSuccessDialog 显示成功对话框
func ShowSuccessDialog(title, message string, parent fyne.Window) {
	// 创建成功信息标签
	successLabel := widget.NewLabel(message)
	successLabel.Wrapping = fyne.TextWrapWord

	// 创建确定按钮
	var customDialog *dialog.CustomDialog
	okButton := widget.NewButton("确定", func() {
		customDialog.Hide()
	})

	// 创建内容容器
	content := container.NewVBox(
		successLabel,
		widget.NewSeparator(),
		container.NewCenter(okButton),
	)

	// 创建对话框 - 使用 NewCustomWithoutButtons 避免空按钮
	customDialog = dialog.NewCustomWithoutButtons(title, content, parent)

	// 根据消息长度动态调整大小
	var dialogSize fyne.Size
	if len(message) <= 30 {
		// 短消息：较小的对话框
		dialogSize = fyne.NewSize(300, 150)
	} else if len(message) <= 100 {
		// 中等消息：中等大小的对话框
		dialogSize = fyne.NewSize(350, 180)
	} else {
		// 长消息：较大的对话框
		dialogSize = fyne.NewSize(400, 220)
	}

	customDialog.Resize(dialogSize)
	customDialog.Show()
}

// ShowErrorDialog 显示错误对话框
func ShowErrorDialog(title string, err error, parent fyne.Window) {
	// 创建错误信息标签
	errorLabel := widget.NewLabel(err.Error())
	errorLabel.Wrapping = fyne.TextWrapWord

	// 创建确定按钮
	var customDialog *dialog.CustomDialog
	okButton := widget.NewButton("确定", func() {
		customDialog.Hide()
	})

	// 创建内容容器
	content := container.NewVBox(
		errorLabel,
		widget.NewSeparator(),
		container.NewCenter(okButton),
	)

	// 创建对话框 - 使用 NewCustomWithoutButtons 避免空按钮
	customDialog = dialog.NewCustomWithoutButtons(title, content, parent)

	// 根据错误信息长度动态调整大小
	errorText := err.Error()
	var dialogSize fyne.Size
	if len(errorText) <= 30 {
		// 短错误信息：较小的对话框
		dialogSize = fyne.NewSize(300, 150)
	} else if len(errorText) <= 100 {
		// 中等错误信息：中等大小的对话框
		dialogSize = fyne.NewSize(350, 180)
	} else {
		// 长错误信息：较大的对话框
		dialogSize = fyne.NewSize(400, 220)
	}

	customDialog.Resize(dialogSize)
	customDialog.Show()
}

// ShowConfirmDialog 显示确认对话框
// 遵循模块化规范：可复用的确认对话框组件
func ShowConfirmDialog(title, message string, onConfirm func(), parent fyne.Window) {
	dialog.ShowConfirm(title, message, func(confirmed bool) {
		if confirmed && onConfirm != nil {
			onConfirm()
		}
	}, parent)
}

// ShowDeleteConfirmDialog 显示删除确认对话框
// 遵循模块化规范：专门的删除确认组件，可复用于主机、主机组等
func ShowDeleteConfirmDialog(itemType, itemName string, onConfirm func(), parent fyne.Window) {
	title := fmt.Sprintf("确认删除%s", itemType)
	message := fmt.Sprintf("确定要删除%s '%s' 吗？\n\n此操作不可撤销！", itemType, itemName)

	dialog.ShowConfirm(title, message, func(confirmed bool) {
		if confirmed && onConfirm != nil {
			onConfirm()
		}
	}, parent)
}

// CreateHostFormDialog 创建主机表单对话框
func CreateHostFormDialog(title string, host *data.HostInfo, onSave func(name, hostAddr, username, password string, port int), parent fyne.Window) {
	// 创建表单字段，设置统一的大小
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("输入主机名称")

	hostEntry := widget.NewEntry()
	hostEntry.SetPlaceHolder("输入主机地址或IP")

	portEntry := widget.NewEntry()
	portEntry.SetText("22")
	portEntry.SetPlaceHolder("SSH端口")

	usernameEntry := widget.NewEntry()
	usernameEntry.SetPlaceHolder("SSH用户名")

	// 连接方式选择
	authTypeSelect := widget.NewSelect([]string{"密码连接", "密钥连接"}, nil)
	authTypeSelect.SetSelected("密码连接")

	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.SetPlaceHolder("SSH密码")

	keyFileEntry := widget.NewEntry()
	keyFileEntry.SetPlaceHolder("私钥文件路径")
	keyFileEntry.Hide() // 默认隐藏

	browseBtn := widget.NewButton("浏览", func() {
		// 创建文件选择对话框
		fileDialog := dialog.NewFileOpen(func(reader fyne.URIReadCloser, err error) {
			if err != nil {
				return
			}
			if reader != nil {
				keyFileEntry.SetText(reader.URI().Path())
				reader.Close()
			}
		}, parent)

		// 设置文件过滤器，只显示常见的密钥文件
		fileDialog.SetFilter(storage.NewExtensionFileFilter([]string{".pem", ".key", ".pub", ".ppk"}))
		fileDialog.Show()
	})
	// 不要单独隐藏browseBtn，让keyContainer控制显示

	keyContainer := container.NewBorder(nil, nil, nil, browseBtn, keyFileEntry)

	// 密钥密码输入框（用于有密码保护的密钥文件）
	keyPassEntry := widget.NewPasswordEntry()
	keyPassEntry.SetPlaceHolder("密钥密码（可选）")
	// 不要单独隐藏keyPassEntry，让keyForm控制显示

	// 如果是编辑模式，填充现有数据
	if host != nil {
		nameEntry.SetText(host.Name)
		hostEntry.SetText(host.Host)
		portEntry.SetText(host.Port) // host.Port已经是字符串
		usernameEntry.SetText(host.Username)
		passwordEntry.SetText(host.Password)
	}

	// 创建动态表单容器
	var currentForm *widget.Form
	formContainer := container.NewVBox()

	// 创建密码模式表单
	createPasswordForm := func() {
		currentForm = widget.NewForm(
			widget.NewFormItem("主机名称", nameEntry),
			widget.NewFormItem("主机地址", hostEntry),
			widget.NewFormItem("SSH端口", portEntry),
			widget.NewFormItem("用户名", usernameEntry),
			widget.NewFormItem("连接方式", authTypeSelect),
			widget.NewFormItem("密码", passwordEntry),
		)
		formContainer.RemoveAll()
		formContainer.Add(currentForm)
	}

	// 创建密钥模式表单
	createKeyForm := func() {
		currentForm = widget.NewForm(
			widget.NewFormItem("主机名称", nameEntry),
			widget.NewFormItem("主机地址", hostEntry),
			widget.NewFormItem("SSH端口", portEntry),
			widget.NewFormItem("用户名", usernameEntry),
			widget.NewFormItem("连接方式", authTypeSelect),
			widget.NewFormItem("密钥文件", keyContainer),
			widget.NewFormItem("密钥密码", keyPassEntry),
		)
		formContainer.RemoveAll()
		formContainer.Add(currentForm)
	}

	// 默认创建密码表单
	createPasswordForm()

	// 连接方式切换逻辑
	authTypeSelect.OnChanged = func(selected string) {
		if selected == "密钥连接" {
			createKeyForm()
		} else {
			createPasswordForm()
		}
	}

	// 创建自定义对话框
	dialog := dialog.NewCustomConfirm(title, "保存", "取消", formContainer, func(confirmed bool) {
		if !confirmed {
			return
		}

		// 验证输入
		name := nameEntry.Text
		hostAddr := hostEntry.Text
		username := usernameEntry.Text
		authType := authTypeSelect.Selected
		password := passwordEntry.Text
		keyFile := keyFileEntry.Text
		keyPass := keyPassEntry.Text

		if name == "" || hostAddr == "" || username == "" {
			ShowErrorDialog("输入错误", fmt.Errorf("主机名称、地址和用户名不能为空"), parent)
			return
		}

		// 根据连接方式验证
		if authType == "密码连接" {
			if password == "" {
				ShowErrorDialog("输入错误", fmt.Errorf("密码不能为空"), parent)
				return
			}
		} else if authType == "密钥连接" {
			if keyFile == "" {
				ShowErrorDialog("输入错误", fmt.Errorf("密钥文件路径不能为空"), parent)
				return
			}
			// 对于密钥连接，密码字段用于存储密钥信息（文件路径和密钥密码）
			if keyPass != "" {
				password = "KEY:" + keyFile + "|PASS:" + keyPass
			} else {
				password = "KEY:" + keyFile
			}
		}

		port, err := strconv.Atoi(portEntry.Text)
		if err != nil || port < 1 || port > 65535 {
			ShowErrorDialog("输入错误", fmt.Errorf("端口号必须是1-65535之间的数字"), parent)
			return
		}

		onSave(name, hostAddr, username, password, port)
	}, parent)

	// 设置对话框大小 - 宽度300px，高度405px
	dialog.Resize(fyne.NewSize(300, 405))
	dialog.Show()
}

// CreateGroupFormDialog 创建主机组表单对话框 - 简化版本
func CreateGroupFormDialog(title string, group *data.HostGroup, hosts []data.HostInfo, onSave func(name, description string, hostIDs []string), parent fyne.Window) {
	// 创建表单字段
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("输入主机组名称")

	descEntry := widget.NewMultiLineEntry()
	descEntry.SetPlaceHolder("输入主机组描述（可选）")

	// 如果是编辑模式，填充现有数据
	if group != nil {
		nameEntry.SetText(group.Name)
		descEntry.SetText(group.Description)
	}

	// 创建表单
	form := &widget.Form{
		Items: []*widget.FormItem{
			{Text: "主机组名称:", Widget: nameEntry},
			{Text: "描述:", Widget: descEntry},
		},
		OnSubmit: func() {
			name := nameEntry.Text
			description := descEntry.Text

			if name == "" {
				ShowErrorDialog("输入错误", fmt.Errorf("主机组名称不能为空"), parent)
				return
			}

			// 暂时返回空的主机ID列表，后续可以扩展
			onSave(name, description, []string{})
		},
	}

	// 显示对话框
	dialog.ShowForm(title, "保存", "取消", form.Items, func(confirmed bool) {
		if confirmed {
			form.OnSubmit()
		}
	}, parent)
}

// CreateTaskFormDialog 创建任务表单对话框 - 简化版本
func CreateTaskFormDialog(hosts []data.HostInfo, groups []data.HostGroup, onSave func(name string, hostIDs []string, concurrency int), parent fyne.Window) {
	// 创建表单字段
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("输入任务名称")

	concurrencyEntry := widget.NewEntry()
	concurrencyEntry.SetText("3")
	concurrencyEntry.SetPlaceHolder("并发数 (1-10)")

	// 创建表单
	form := &widget.Form{
		Items: []*widget.FormItem{
			{Text: "任务名称:", Widget: nameEntry},
			{Text: "并发数:", Widget: concurrencyEntry},
		},
		OnSubmit: func() {
			name := nameEntry.Text
			if name == "" {
				ShowErrorDialog("输入错误", fmt.Errorf("任务名称不能为空"), parent)
				return
			}

			concurrency, err := strconv.Atoi(concurrencyEntry.Text)
			if err != nil || concurrency < 1 || concurrency > 10 {
				ShowErrorDialog("输入错误", fmt.Errorf("并发数必须是1-10之间的数字"), parent)
				return
			}

			// 暂时使用所有主机ID，后续可以扩展选择功能
			var hostIDs []string
			for _, host := range hosts {
				hostIDs = append(hostIDs, host.ID)
			}

			if len(hostIDs) == 0 {
				ShowErrorDialog("选择错误", fmt.Errorf("没有可用的主机"), parent)
				return
			}

			onSave(name, hostIDs, concurrency)
		},
	}

	// 显示对话框
	dialog.ShowForm("创建扫描任务", "创建", "取消", form.Items, func(confirmed bool) {
		if confirmed {
			form.OnSubmit()
		}
	}, parent)
}

// ShowAddGroupWithHostsDialog 显示添加主机组对话框（组内新建主机）
func ShowAddGroupWithHostsDialog(onSave func(groupName, groupDesc string, hosts []GroupHostInfo), parent fyne.Window) {
	ShowGroupWithHostsDialog("添加主机组", nil, onSave, parent)
}

// ShowEditGroupWithHostsDialog 显示编辑主机组对话框（组内编辑主机）
func ShowEditGroupWithHostsDialog(group *data.HostGroup, onSave func(groupName, groupDesc string, hosts []GroupHostInfo), parent fyne.Window) {
	ShowGroupWithHostsDialog("编辑主机组", group, onSave, parent)
}

// ShowGroupWithHostsDialog 显示主机组对话框（通用版本，支持添加和编辑）
func ShowGroupWithHostsDialog(title string, group *data.HostGroup, onSave func(groupName, groupDesc string, hosts []GroupHostInfo), parent fyne.Window) {
	if parent == nil {
		return
	}

	// 使用defer来捕获panic
	defer func() {
		if r := recover(); r != nil {
			ShowErrorDialog("对话框错误", fmt.Errorf("对话框创建失败: %v", r), parent)
		}
	}()

	// 创建主机组基本信息输入字段
	groupNameEntry := widget.NewEntry()
	groupNameEntry.SetPlaceHolder("主机组名称")
	groupNameEntry.Resize(fyne.NewSize(250, 25))

	// 使用单行输入框代替多行输入框，这样高度更小
	groupDescEntry := widget.NewEntry()
	groupDescEntry.SetPlaceHolder("主机组描述（可选）")
	groupDescEntry.Resize(fyne.NewSize(250, 25))

	// 如果是编辑模式，填充现有数据
	if group != nil {
		groupNameEntry.SetText(group.Name)
		groupDescEntry.SetText(group.Description)
	}

	// 创建组内主机列表容器
	var groupHosts []GroupHostInfo
	hostListContainer := container.NewVBox()

	// 如果是编辑模式，预填充现有主机
	if group != nil {
		for _, host := range group.Hosts {
			hostInfo := GroupHostInfo{
				NameEntry: widget.NewEntry(),
				AddrEntry: widget.NewEntry(),
				UserEntry: widget.NewEntry(),
				PassEntry: widget.NewEntry(),
			}

			// 设置现有主机的值
			hostInfo.NameEntry.SetText(host.Name)
			hostInfo.AddrEntry.SetText(host.Host)
			hostInfo.UserEntry.SetText(host.Username)
			hostInfo.PassEntry.SetText(host.Password)

			groupHosts = append(groupHosts, hostInfo)
		}
	}

	// 更新主机列表显示
	var updateHostList func()
	updateHostList = func() {
		hostListContainer.RemoveAll()
		for i, hostInfo := range groupHosts {
			// 获取主机信息
			hostName := hostInfo.NameEntry.Text
			hostAddr := hostInfo.AddrEntry.Text
			hostUser := hostInfo.UserEntry.Text

			// 确保信息不为空
			if hostName == "" {
				hostName = "未命名主机"
			}
			if hostAddr == "" {
				hostAddr = "未设置地址"
			}
			if hostUser == "" {
				hostUser = "未设置用户"
			}

			// 主机信息标签 - 单行显示
			infoText := fmt.Sprintf("%d. %s (%s@%s)", i+1, hostName, hostUser, hostAddr)
			hostLabel := widget.NewLabel(infoText)
			// 不设置换行，默认就是单行显示
			hostLabel.Resize(fyne.NewSize(200, 22)) // 缩小标签宽度为按钮留空间

			// 编辑按钮
			editBtn := widget.NewButton("编辑", nil)
			editBtn.Resize(fyne.NewSize(40, 22))
			hostIndex := i // 捕获索引
			editBtn.OnTapped = func() {
				// 获取当前主机信息
				currentHost := groupHosts[hostIndex]

				// 创建编辑用的主机信息结构
				editHost := &data.HostInfo{
					Name:     currentHost.NameEntry.Text,
					Host:     currentHost.AddrEntry.Text,
					Username: currentHost.UserEntry.Text,
					Password: currentHost.PassEntry.Text,
					Port:     "22",
				}

				// 弹出编辑对话框
				CreateHostFormDialog("编辑组内主机", editHost, func(name, hostAddr, username, password string, port int) {
					// 检查主机名是否与其他主机重复（排除自己）
					trimmedName := strings.TrimSpace(name)
					for j, existingHost := range groupHosts {
						if j != hostIndex && strings.TrimSpace(existingHost.NameEntry.Text) == trimmedName {
							ShowErrorDialog("编辑失败", fmt.Errorf("主机名 '%s' 已存在于当前主机组中，请使用不同的名称", trimmedName), parent)
							return
						}
					}

					// 更新主机信息
					groupHosts[hostIndex].NameEntry.SetText(name)
					groupHosts[hostIndex].AddrEntry.SetText(hostAddr)
					groupHosts[hostIndex].UserEntry.SetText(username)
					groupHosts[hostIndex].PassEntry.SetText(password)

					// 刷新显示
					updateHostList()
				}, parent)
			}

			// 删除按钮
			removeBtn := widget.NewButton("删除", nil)
			removeBtn.Resize(fyne.NewSize(40, 22))
			hostIndex2 := i // 为删除按钮单独捕获索引
			removeBtn.OnTapped = func() {
				// 从列表中移除
				groupHosts = append(groupHosts[:hostIndex2], groupHosts[hostIndex2+1:]...)
				updateHostList()
			}

			// 创建按钮容器
			buttonContainer := container.NewHBox(editBtn, removeBtn)

			// 使用Border布局，确保按钮在右边
			hostRow := container.NewBorder(nil, nil, hostLabel, buttonContainer)

			hostListContainer.Add(hostRow)
		}

		if len(groupHosts) == 0 {
			hostListContainer.Add(widget.NewLabel("暂无主机，请点击下方按钮添加"))
		}

		hostListContainer.Refresh()
	}

	// 添加主机到组的函数 - 弹出对话框
	addHostToGroup := func() {
		CreateHostFormDialog("添加主机到组", nil, func(name, hostAddr, username, password string, port int) {
			// 检查主机名是否重复
			trimmedName := strings.TrimSpace(name)
			for _, existingHost := range groupHosts {
				if strings.TrimSpace(existingHost.NameEntry.Text) == trimmedName {
					ShowErrorDialog("添加失败", fmt.Errorf("主机名 '%s' 已存在于当前主机组中，请使用不同的名称", trimmedName), parent)
					return
				}
			}

			// 创建主机信息结构
			hostInfo := GroupHostInfo{
				NameEntry: widget.NewEntry(),
				AddrEntry: widget.NewEntry(),
				UserEntry: widget.NewEntry(),
				PassEntry: widget.NewEntry(),
			}

			// 设置值
			hostInfo.NameEntry.SetText(name)
			hostInfo.AddrEntry.SetText(hostAddr)
			hostInfo.UserEntry.SetText(username)
			hostInfo.PassEntry.SetText(password)

			// 添加到列表
			groupHosts = append(groupHosts, hostInfo)
			updateHostList()
		}, parent)
	}

	// 添加主机按钮
	addHostBtn := widget.NewButton("+ 添加主机到组", func() {
		addHostToGroup()
	})

	// 初始化主机列表显示
	updateHostList()

	// 主机列表滚动容器 - 设置固定高度和滚动行为
	hostScroll := container.NewScroll(hostListContainer)
	hostScroll.SetMinSize(fyne.NewSize(320, 200))

	// 创建一个固定高度的容器来包装滚动区域
	scrollWrapper := container.NewBorder(nil, nil, nil, nil, hostScroll)
	scrollWrapper.Resize(fyne.NewSize(320, 200))

	// 创建完整表单 - 使用VBox垂直布局
	form := container.NewVBox(
		// 主机组信息区域
		widget.NewLabel("主机组信息"),
		// 使用简单的Form布局
		widget.NewForm(
			widget.NewFormItem("组名称", groupNameEntry),
			widget.NewFormItem("组描述", groupDescEntry),
		),
		widget.NewSeparator(),
		// 主机列表区域
		widget.NewLabel("组内主机列表"),
		addHostBtn,
		scrollWrapper, // 固定高度的主机列表区域
	)

	// 创建对话框，使用传入的标题
	buttonText := "创建"
	if group != nil {
		buttonText = "保存"
	}
	dialog := dialog.NewCustomConfirm(title, buttonText, "取消", form, func(confirmed bool) {
		if !confirmed {
			return
		}

		// 验证主机组名称
		if groupNameEntry.Text == "" {
			ShowErrorDialog("输入错误", fmt.Errorf("主机组名称不能为空"), parent)
			return
		}

		// 验证组内主机信息
		if len(groupHosts) == 0 {
			ShowErrorDialog("输入错误", fmt.Errorf("主机组至少需要包含一个主机"), parent)
			return
		}

		// 调用保存回调
		onSave(groupNameEntry.Text, groupDescEntry.Text, groupHosts)
	}, parent)

	dialog.Resize(fyne.NewSize(350, 420))
	dialog.Show()
}

// ShowGroupConnectionTestDialog 显示主机组连接测试结果对话框
// 遵循模块化规范：可复用的测试结果显示组件
func ShowGroupConnectionTestDialog(groupName string, results []HostConnectionResult, parent fyne.Window) {
	// 创建表格数据
	tableData := make([][]string, len(results)+1)

	// 表头
	tableData[0] = []string{"主机名", "地址", "状态", "详情"}

	// 填充数据
	for i, result := range results {
		status := "❌ 失败"
		detail := result.Error
		if result.Success {
			status = "✅ 成功"
			detail = fmt.Sprintf("耗时: %v", result.Duration)
		}

		tableData[i+1] = []string{
			result.HostName,
			fmt.Sprintf("%s:%s", result.HostAddr, result.HostPort),
			status,
			detail,
		}
	}

	// 创建表格
	resultTable := widget.NewTable(
		func() (int, int) {
			return len(tableData), 4 // 行数，列数
		},
		func() fyne.CanvasObject {
			label := widget.NewLabel("")
			label.Wrapping = fyne.TextWrapWord // 启用文本换行
			return label
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			label := obj.(*widget.Label)
			if id.Row < len(tableData) && id.Col < len(tableData[id.Row]) {
				text := tableData[id.Row][id.Col]
				label.SetText(text)

				// 设置表头样式
				if id.Row == 0 {
					label.TextStyle = fyne.TextStyle{Bold: true}
				}

				// 设置状态列的颜色
				if id.Col == 2 && id.Row > 0 { // 状态列
					if tableData[id.Row][id.Col] == "✅ 成功" {
						label.Importance = widget.SuccessImportance
					} else {
						label.Importance = widget.DangerImportance
					}
				}
			}
		},
	)

	// 设置列宽 - 确保总宽度不超过对话框宽度，避免左右滚动
	resultTable.SetColumnWidth(0, 100) // 主机名
	resultTable.SetColumnWidth(1, 120) // 地址
	resultTable.SetColumnWidth(2, 70)  // 状态
	resultTable.SetColumnWidth(3, 180) // 详情
	// 总宽度: 100+120+70+180 = 470px，小于对话框宽度580px

	// 统计结果
	successCount := 0
	failCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		} else {
			failCount++
		}
	}

	// 创建统计信息
	summaryText := fmt.Sprintf("主机组 '%s' 连接测试结果\n\n总计: %d 台主机\n✅ 成功: %d 台\n❌ 失败: %d 台",
		groupName, len(results), successCount, failCount)
	summaryLabel := widget.NewLabel(summaryText)
	summaryLabel.Wrapping = fyne.TextWrapWord

	// 创建固定宽度的列表显示，模拟表格效果但避免左右滚动
	resultContainer := container.NewVBox()

	// 添加表头 - 使用固定宽度的标签
	headerName := widget.NewLabelWithStyle("主机名", fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
	headerName.Resize(fyne.NewSize(100, 25))

	headerAddr := widget.NewLabelWithStyle("地址", fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
	headerAddr.Resize(fyne.NewSize(120, 25))

	headerStatus := widget.NewLabelWithStyle("状态", fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
	headerStatus.Resize(fyne.NewSize(70, 25))

	headerDetail := widget.NewLabelWithStyle("详情", fyne.TextAlignLeading, fyne.TextStyle{Bold: true})
	headerDetail.Resize(fyne.NewSize(180, 25))

	headerContainer := container.NewWithoutLayout(headerName, headerAddr, headerStatus, headerDetail)
	headerName.Move(fyne.NewPos(0, 0))
	headerAddr.Move(fyne.NewPos(105, 0))
	headerStatus.Move(fyne.NewPos(230, 0))
	headerDetail.Move(fyne.NewPos(305, 0))
	headerContainer.Resize(fyne.NewSize(485, 25))

	resultContainer.Add(headerContainer)
	resultContainer.Add(widget.NewSeparator())

	// 添加每行数据 - 使用固定宽度和位置
	for _, result := range results {
		status := "❌ 失败"
		detail := result.Error
		statusImportance := widget.DangerImportance

		if result.Success {
			status = "✅ 成功"
			detail = fmt.Sprintf("耗时: %v", result.Duration)
			statusImportance = widget.SuccessImportance
		}

		// 创建固定宽度的标签
		nameLabel := widget.NewLabel(result.HostName)
		nameLabel.Resize(fyne.NewSize(100, 25))

		addrLabel := widget.NewLabel(fmt.Sprintf("%s:%s", result.HostAddr, result.HostPort))
		addrLabel.Resize(fyne.NewSize(120, 25))

		statusLabel := widget.NewLabel(status)
		statusLabel.Importance = statusImportance
		statusLabel.Resize(fyne.NewSize(70, 25))

		detailLabel := widget.NewLabel(detail)
		detailLabel.Wrapping = fyne.TextWrapWord
		detailLabel.Resize(fyne.NewSize(180, 50)) // 增加高度以支持换行

		// 使用无布局容器精确定位
		rowContainer := container.NewWithoutLayout(nameLabel, addrLabel, statusLabel, detailLabel)
		nameLabel.Move(fyne.NewPos(0, 0))
		addrLabel.Move(fyne.NewPos(105, 0))
		statusLabel.Move(fyne.NewPos(230, 0))
		detailLabel.Move(fyne.NewPos(305, 0))
		rowContainer.Resize(fyne.NewSize(485, 50))

		resultContainer.Add(rowContainer)
	}

	// 创建滚动容器 - 宽度匹配内容，高度固定
	resultScroll := container.NewScroll(resultContainer)
	resultScroll.SetMinSize(fyne.NewSize(485, 250))

	// 创建内容容器
	content := container.NewVBox(
		summaryLabel,
		widget.NewSeparator(),
		widget.NewLabel("详细结果:"),
		resultScroll,
	)

	// 创建对话框 - 使用 NewCustomWithoutButtons 避免空按钮
	var customDialog *dialog.CustomDialog
	closeButton := widget.NewButton("关闭", func() {
		customDialog.Hide()
	})

	// 创建完整内容，包含自定义关闭按钮
	fullContent := container.NewVBox(
		content,
		widget.NewSeparator(),
		container.NewCenter(closeButton),
	)

	customDialog = dialog.NewCustomWithoutButtons("主机组连接测试结果", fullContent, parent)
	customDialog.Resize(fyne.NewSize(530, 450)) // 对话框宽度适配新布局，留有边距
	customDialog.Show()
}

// HostConnectionResult 主机连接测试结果
type HostConnectionResult struct {
	HostName string
	HostAddr string
	HostPort string
	Success  bool
	Error    string
	Duration time.Duration
}

// ProgressDialogData 进度对话框数据
type ProgressDialogData struct {
	ProgressBar    *widget.ProgressBar
	InfiniteBar    *widget.ProgressBarInfinite
	StatusLabel    *widget.Label
	Dialog         *dialog.CustomDialog
	CancelButton   *widget.Button
	CloseButton    *widget.Button
	Cancelled      bool
	IsInfiniteMode bool
}

// ShowProgressDialog 显示进度对话框
func ShowProgressDialog(title string, parent fyne.Window) *ProgressDialogData {
	// 创建动态进度条（无限进度条，能动的）
	progressBar := widget.NewProgressBarInfinite()
	progressBar.Start()
	// 设置进度条的最小尺寸
	progressBar.Resize(fyne.NewSize(350, 25))

	// 创建状态标签
	statusLabel := widget.NewLabel("正在准备测试...")
	statusLabel.Alignment = fyne.TextAlignCenter

	// 创建进度数据结构
	data := &ProgressDialogData{
		ProgressBar:    nil,
		InfiniteBar:    progressBar,
		StatusLabel:    statusLabel,
		Cancelled:      false,
		IsInfiniteMode: true,
	}

	// 创建取消按钮
	cancelButton := widget.NewButton("取消", func() {
		data.Cancelled = true
		data.StatusLabel.SetText("正在取消测试...")
		data.CancelButton.Disable()
		if data.IsInfiniteMode && data.InfiniteBar != nil {
			data.InfiniteBar.Stop()
		}
	})

	// 创建关闭按钮
	closeButton := widget.NewButton("关闭", func() {
		if data.IsInfiniteMode && data.InfiniteBar != nil {
			data.InfiniteBar.Stop()
		}
		data.Dialog.Hide()
	})

	data.CancelButton = cancelButton
	data.CloseButton = closeButton

	// 创建按钮容器 - 取消和关闭按钮在同一行，居中显示，有适当间距
	buttonContainer := container.NewHBox(
		cancelButton,
		widget.NewLabel("   "), // 添加间距
		closeButton,
	)

	// 创建内容容器
	content := container.NewVBox(
		widget.NewLabel(title),
		widget.NewSeparator(),
		statusLabel,
		progressBar,
		widget.NewSeparator(),
		container.NewCenter(buttonContainer),
	)

	// 创建对话框 - 使用 NewCustomWithoutButtons 避免默认按钮
	customDialog := dialog.NewCustomWithoutButtons("连接测试进度", content, parent)
	customDialog.Resize(fyne.NewSize(400, 200))
	data.Dialog = customDialog

	return data
}

// ShowSingleHostProgressDialog 显示单个主机测试的进度对话框
func ShowSingleHostProgressDialog(title string, hostName string, parent fyne.Window) *ProgressDialogData {
	// 创建进度条（无限进度条，因为单个主机测试时间不确定）
	progressBar := widget.NewProgressBarInfinite()
	progressBar.Start()

	// 创建状态标签
	statusLabel := widget.NewLabel(fmt.Sprintf("正在测试主机: %s", hostName))
	statusLabel.Alignment = fyne.TextAlignCenter

	// 创建进度数据结构
	data := &ProgressDialogData{
		ProgressBar:    nil,
		InfiniteBar:    progressBar,
		StatusLabel:    statusLabel,
		Cancelled:      false,
		IsInfiniteMode: true,
	}

	// 创建取消按钮
	cancelButton := widget.NewButton("取消", func() {
		data.Cancelled = true
		data.StatusLabel.SetText("正在取消测试...")
		data.CancelButton.Disable()
		progressBar.Stop()
		data.Dialog.Hide()
	})

	// 创建关闭按钮
	closeButton := widget.NewButton("关闭", func() {
		progressBar.Stop()
		data.Dialog.Hide()
	})

	data.CancelButton = cancelButton
	data.CloseButton = closeButton

	// 创建按钮容器 - 只显示取消按钮，关闭按钮隐藏
	buttonContainer := container.NewHBox(
		cancelButton,
	)

	// 创建内容容器
	content := container.NewVBox(
		widget.NewLabel(title),
		widget.NewSeparator(),
		statusLabel,
		progressBar,
		widget.NewSeparator(),
		container.NewCenter(buttonContainer),
	)

	// 创建对话框 - 使用 NewCustomWithoutButtons 避免默认按钮
	customDialog := dialog.NewCustomWithoutButtons("连接测试进度", content, parent)
	customDialog.Resize(fyne.NewSize(350, 180))
	data.Dialog = customDialog

	return data
}

// UpdateProgressDialog 更新进度对话框
func UpdateProgressDialog(data *ProgressDialogData, current, total int, currentHost string) {
	if data == nil || data.ProgressBar == nil || data.StatusLabel == nil {
		return
	}

	// 更新进度条
	progress := float64(current) / float64(total)
	data.ProgressBar.SetValue(progress)

	// 更新状态文本
	data.StatusLabel.SetText(fmt.Sprintf("正在测试: %s (%d/%d)", currentHost, current+1, total))
}

// UpdateProgressDialogWithStats 更新进度对话框（带统计信息）
func UpdateProgressDialogWithStats(data *ProgressDialogData, current, total, success, failed int, currentHost string) {
	if data == nil || data.ProgressBar == nil || data.StatusLabel == nil {
		return
	}

	// 更新进度条
	progress := float64(current) / float64(total)
	data.ProgressBar.SetValue(progress)

	// 更新状态文本，包含统计信息
	statusText := fmt.Sprintf("正在测试: %s (%d/%d)\n✅ 成功: %d  ❌ 失败: %d",
		currentHost, current+1, total, success, failed)
	data.StatusLabel.SetText(statusText)
}
