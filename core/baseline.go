package core

import (
	"fmt"
	"time"

	"lcheck/checkers"
	"lcheck/data"
)

// BaselineChecker 基线检查器 - 真正的模块化管理器
type BaselineChecker struct {
	sshClient SSHClientInterface
	manager   *checkers.Manager
	timeout   time.Duration
}

// NewBaselineChecker 创建基线检查器 - 使用模块化管理器
func NewBaselineChecker(sshClient SSHClientInterface) *BaselineChecker {
	return &BaselineChecker{
		sshClient: sshClient,
		manager:   checkers.NewManager(),
		timeout:   30 * time.Second, // 默认超时时间
	}
}

// RunAllChecks 运行所有检查项 - 委托给管理器
func (bc *BaselineChecker) RunAllChecks(host data.HostInfo) []data.BaselineCheckResult {
	fmt.Printf("开始对主机 %s 进行基线检查...\n", host.Host)

	results := bc.manager.RunAllChecks(bc.sshClient, host)

	fmt.Printf("主机 %s 基线检查完成，共执行 %d 项检查\n", host.Host, len(results))
	return results
}

// RunCheck 运行特定检查项 - 委托给管理器
func (bc *BaselineChecker) RunCheck(host data.HostInfo, checkID string) (*data.BaselineCheckResult, error) {
	return bc.manager.RunSpecificCheck(bc.sshClient, host, checkID)
}

// GetAllChecks 获取所有检查项 - 委托给管理器
func (bc *BaselineChecker) GetAllChecks() []data.BaselineCheck {
	return bc.manager.GetAllChecks()
}

// GetCheckByID 根据ID获取检查项 - 委托给管理器
func (bc *BaselineChecker) GetCheckByID(checkID string) *data.BaselineCheck {
	return bc.manager.GetCheckByID(checkID)
}

// GetChecksByCategory 根据类别获取检查项 - 委托给管理器
func (bc *BaselineChecker) GetChecksByCategory(category string) []data.BaselineCheck {
	return bc.manager.GetChecksByCategory(category)
}

// GetCategories 获取所有类别 - 委托给管理器
func (bc *BaselineChecker) GetCategories() []string {
	return bc.manager.GetCategories()
}

// GetCheckCount 获取检查项总数 - 委托给管理器
func (bc *BaselineChecker) GetCheckCount() int {
	return bc.manager.GetCheckCount()
}

// ValidateHost 验证主机连接
func (bc *BaselineChecker) ValidateHost(host data.HostInfo) error {
	_, err := bc.sshClient.ExecuteCommand(host, "echo 'connection test'")
	if err != nil {
		return fmt.Errorf("无法连接到主机 %s: %v", host.Host, err)
	}
	return nil
}

// CheckHost 检查主机 - 实现BaselineCheckerInterface接口
func (bc *BaselineChecker) CheckHost(host data.HostInfo) (*data.ScanResult, error) {
	result := &data.ScanResult{
		HostID:       host.ID,
		HostName:     host.Name,
		Host:         host.Host,
		StartTime:    time.Now(),
		Status:       "运行中",
		CheckResults: make([]data.BaselineCheckResult, 0),
	}

	// 运行所有检查
	checkResults := bc.RunAllChecks(host)
	result.CheckResults = checkResults
	result.Status = "已完成"

	return result, nil
}

// CheckHostWithContext 带上下文检查主机 - 实现BaselineCheckerInterface接口
func (bc *BaselineChecker) CheckHostWithContext(host data.HostInfo, ctx interface{}) (*data.ScanResult, error) {
	// 简化实现，忽略上下文
	return bc.CheckHost(host)
}

// GetAvailableCheckers 获取可用检查器 - 实现BaselineCheckerInterface接口
func (bc *BaselineChecker) GetAvailableCheckers() []string {
	var checkers []string
	for _, category := range bc.GetCategories() {
		checkers = append(checkers, category)
	}
	return checkers
}

// EnableChecker 启用检查器 - 实现BaselineCheckerInterface接口
func (bc *BaselineChecker) EnableChecker(checkerName string) error {
	// 简化实现，总是返回成功
	return nil
}

// DisableChecker 禁用检查器 - 实现BaselineCheckerInterface接口
func (bc *BaselineChecker) DisableChecker(checkerName string) error {
	// 简化实现，总是返回成功
	return nil
}

// IsCheckerEnabled 检查器是否启用 - 实现BaselineCheckerInterface接口
func (bc *BaselineChecker) IsCheckerEnabled(checkerName string) bool {
	// 简化实现，总是返回启用
	return true
}

// SetTimeout 设置超时时间 - 实现BaselineCheckerInterface接口
func (bc *BaselineChecker) SetTimeout(timeout time.Duration) {
	bc.timeout = timeout
}

// GetTimeout 获取超时时间 - 实现BaselineCheckerInterface接口
func (bc *BaselineChecker) GetTimeout() time.Duration {
	return bc.timeout
}
