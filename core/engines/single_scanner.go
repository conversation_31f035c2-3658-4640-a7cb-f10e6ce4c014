package engines

import (
	"fmt"
	"time"

	"lcheck/data"
)

// SSHClientInterface SSH客户端接口 - 避免循环依赖
type SSHClientInterface interface {
	TestConnection(host data.HostInfo) error
	ExecuteCommand(host data.HostInfo, command string) (string, error)
	ExecuteCommands(host data.HostInfo, commands map[string]string) (map[string]string, error)
	FileExists(host data.HostInfo, filePath string) (bool, error)
	GetFileContent(host data.HostInfo, filePath string) (string, error)
	SetTimeout(timeout time.Duration)
	GetTimeout() time.Duration
}

// BaselineCheckerInterface 基线检查器接口 - 避免循环依赖
type BaselineCheckerInterface interface {
	CheckHost(host data.HostInfo) (*data.ScanResult, error)
	ValidateHost(host data.HostInfo) error
	RunAllChecks(host data.HostInfo) []data.BaselineCheckResult
	SetTimeout(timeout time.Duration)
	GetTimeout() time.Duration
}

// SingleScanner 单主机扫描器 - 专门负责单个主机的扫描
type SingleScanner struct {
	sshClient       SSHClientInterface
	baselineChecker BaselineCheckerInterface
	scanTimeout     time.Duration
}

// NewSingleScanner 创建单主机扫描器
func NewSingleScanner(sshClient SSHClientInterface, baselineChecker BaselineCheckerInterface, timeout time.Duration) *SingleScanner {
	return &SingleScanner{
		sshClient:       sshClient,
		baselineChecker: baselineChecker,
		scanTimeout:     timeout,
	}
}

// ScanHost 扫描单个主机
func (ss *SingleScanner) ScanHost(host data.HostInfo) (*data.ScanResult, error) {

	result := &data.ScanResult{
		HostID:       host.ID,
		HostName:     host.Name,
		Host:         host.Host,
		StartTime:    time.Now(),
		Status:       "运行中",
		CheckResults: make([]data.BaselineCheckResult, 0),
	}

	// 验证主机连接
	if err := ss.baselineChecker.ValidateHost(host); err != nil {
		result.Status = "连接失败"
		result.Error = err.Error()
		return result, err
	}

	// 执行基线检查
	checkResults := ss.baselineChecker.RunAllChecks(host)
	result.CheckResults = checkResults

	// 计算总体得分
	totalScore := 0
	validResults := 0
	for _, checkResult := range checkResults {
		if checkResult.Score >= 0 {
			totalScore += checkResult.Score
			validResults++
		}
	}

	if validResults > 0 {
		result.TotalScore = totalScore / validResults
	}

	// 设置最终状态
	if result.Error == "" {
		result.Status = "已完成"
	} else {
		result.Status = "部分完成"
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	return result, nil
}

// ScanHostWithProgress 带进度回调的主机扫描
func (ss *SingleScanner) ScanHostWithProgress(host data.HostInfo, progressCallback func(float64)) (*data.ScanResult, error) {
	// 发送开始进度
	if progressCallback != nil {
		progressCallback(0.0)
	}

	result, err := ss.ScanHost(host)

	// 发送完成进度
	if progressCallback != nil {
		progressCallback(1.0)
	}

	return result, err
}

// ValidateHost 验证主机配置
func (ss *SingleScanner) ValidateHost(host data.HostInfo) error {
	if host.ID == "" {
		return fmt.Errorf("主机ID不能为空")
	}
	if host.Name == "" {
		return fmt.Errorf("主机名不能为空")
	}
	if host.Host == "" {
		return fmt.Errorf("主机地址不能为空")
	}
	if host.Port == "" {
		return fmt.Errorf("主机端口不能为空")
	}
	if host.Username == "" {
		return fmt.Errorf("用户名不能为空")
	}
	return nil
}

// TestConnection 测试主机连接
func (ss *SingleScanner) TestConnection(host data.HostInfo) error {
	return ss.baselineChecker.ValidateHost(host)
}

// GetScanInfo 获取扫描信息
func (ss *SingleScanner) GetScanInfo() map[string]interface{} {
	return map[string]interface{}{
		"type":    "single_scanner",
		"timeout": ss.scanTimeout.String(),
	}
}

// ========== 实现ScanEngineInterface接口 ==========

// ScanHosts 扫描多个主机（单主机扫描器的批量实现）
func (ss *SingleScanner) ScanHosts(hosts []data.HostInfo) ([]data.ScanResult, error) {
	var results []data.ScanResult
	for _, host := range hosts {
		result, err := ss.ScanHost(host)
		if err != nil {
			// 记录错误但继续处理其他主机
			result = &data.ScanResult{
				HostID:    host.ID,
				HostName:  host.Name,
				Host:      host.Host,
				Status:    "失败",
				Error:     err.Error(),
				StartTime: time.Now(),
			}
		}
		results = append(results, *result)
	}
	return results, nil
}

// IsScanning 检查是否正在扫描
func (ss *SingleScanner) IsScanning() bool {
	// 简化实现，单主机扫描器没有持久状态
	return false
}

// Stop 停止扫描
func (ss *SingleScanner) Stop() error {
	// 简化实现，单主机扫描器没有持久状态
	return nil
}

// SetTimeout 设置超时时间
func (ss *SingleScanner) SetTimeout(timeout time.Duration) {
	ss.scanTimeout = timeout
	ss.baselineChecker.SetTimeout(timeout)
}

// GetTimeout 获取超时时间
func (ss *SingleScanner) GetTimeout() time.Duration {
	return ss.scanTimeout
}
