package engines

import (
	"fmt"
	"sync"
	"time"

	"lcheck/data"
)

// ScanContext 扫描上下文
type ScanContext struct {
	ScanID    string
	Status    string
	StartTime time.Time
	Progress  float64
	Cancel    chan bool
}

// BatchScannerInterface 批量扫描器接口 - 避免循环依赖
type BatchScannerInterface interface {
	ScanHosts(hosts []data.HostInfo) ([]data.ScanResult, error)
	ScanHostsWithConcurrency(hosts []data.HostInfo, concurrency int) ([]data.ScanResult, error)
	ScanHostsWithProgress(hosts []data.HostInfo, progressCallback func(int, int)) ([]data.ScanResult, error)
	SetMaxConcurrency(maxConcurrency int)
	GetMaxConcurrency() int
	IsScanning() bool
	Stop() error
	GetScanInfo() map[string]interface{}
}

// ScanManager 扫描管理器 - 管理所有扫描任务
type ScanManager struct {
	singleScanner SingleScannerInterface
	batchScanner  BatchScannerInterface

	// 活动扫描管理
	activeScans map[string]*ScanContext
	mutex       sync.RWMutex

	// 进度通道
	progressChan chan data.ScanProgress
}

// NewScanManager 创建扫描管理器
func NewScanManager(singleScanner SingleScannerInterface, batchScanner BatchScannerInterface) *ScanManager {
	return &ScanManager{
		singleScanner: singleScanner,
		batchScanner:  batchScanner,
		activeScans:   make(map[string]*ScanContext),
		progressChan:  make(chan data.ScanProgress, 100),
	}
}

// GetProgressChannel 获取进度通道
func (sm *ScanManager) GetProgressChannel() <-chan data.ScanProgress {
	return sm.progressChan
}

// StartSingleScan 启动单主机扫描
func (sm *ScanManager) StartSingleScan(host data.HostInfo) (string, error) {
	scanID := fmt.Sprintf("single_%s_%d", host.ID, time.Now().Unix())

	// 创建扫描上下文
	ctx := &ScanContext{
		ScanID:    scanID,
		Status:    "运行中",
		StartTime: time.Now(),
		Progress:  0.0,
		Cancel:    make(chan bool, 1),
	}

	// 注册活动扫描
	sm.mutex.Lock()
	sm.activeScans[scanID] = ctx
	sm.mutex.Unlock()

	// 启动扫描协程
	go func() {
		defer func() {
			// 清理活动扫描
			sm.mutex.Lock()
			delete(sm.activeScans, scanID)
			sm.mutex.Unlock()
		}()

		// 执行扫描
		result, err := sm.singleScanner.ScanHostWithProgress(host, func(progress float64) {
			// 更新上下文
			sm.mutex.Lock()
			if ctx, exists := sm.activeScans[scanID]; exists {
				ctx.Progress = progress
				ctx.Status = "扫描中"
			}
			sm.mutex.Unlock()

			// 发送进度
			progressData := data.ScanProgress{
				HostID:   host.ID,
				HostName: host.Name,
				Progress: int(progress * 100),
				Status:   "扫描中",
			}
			select {
			case sm.progressChan <- progressData:
			default:
				// 通道满了，跳过这次进度更新
			}
		})

		// 发送最终结果
		finalProgress := data.ScanProgress{
			HostID:   host.ID,
			HostName: host.Name,
			Progress: 100,
		}

		if err != nil {
			finalProgress.Status = "扫描失败"
			finalProgress.Message = err.Error()
		} else {
			finalProgress.Status = "扫描完成"
			finalProgress.Message = fmt.Sprintf("主机 %s 扫描完成，得分: %d", host.Name, result.TotalScore)
		}

		select {
		case sm.progressChan <- finalProgress:
		default:
		}
	}()

	return scanID, nil
}

// StartBatchScan 启动批量扫描
func (sm *ScanManager) StartBatchScan(hosts []data.HostInfo, concurrency int) (string, error) {
	scanID := fmt.Sprintf("batch_%d", time.Now().Unix())

	// 创建扫描上下文
	ctx := &ScanContext{
		ScanID:    scanID,
		Status:    "运行中",
		StartTime: time.Now(),
		Progress:  0.0,
		Cancel:    make(chan bool, 1),
	}

	// 注册活动扫描
	sm.mutex.Lock()
	sm.activeScans[scanID] = ctx
	sm.mutex.Unlock()

	// 启动扫描协程
	go func() {
		defer func() {
			// 清理活动扫描
			sm.mutex.Lock()
			delete(sm.activeScans, scanID)
			sm.mutex.Unlock()
		}()

		// 执行批量扫描
		results, err := sm.batchScanner.ScanHosts(hosts)

		// 简化处理，创建批量结果
		result := &data.BatchScanResult{
			ID:             scanID,
			Status:         "已完成",
			TotalHosts:     len(hosts),
			CompletedHosts: len(results),
			Results:        results,
			StartTime:      time.Now(),
			EndTime:        time.Now(),
		}

		if err != nil {
			result.Status = "失败"
		}

		// 发送最终结果
		finalProgress := data.ScanProgress{
			HostID:   "batch",
			Progress: 100,
		}

		if err != nil {
			finalProgress.Status = "批量扫描失败"
			finalProgress.Message = err.Error()
		} else {
			finalProgress.Status = "批量扫描完成"
			finalProgress.Message = fmt.Sprintf("批量扫描完成，成功: %d，失败: %d",
				result.CompletedHosts-result.FailedHosts,
				result.FailedHosts)
		}

		select {
		case sm.progressChan <- finalProgress:
		default:
		}
	}()

	return scanID, nil
}

// CancelScan 取消扫描
func (sm *ScanManager) CancelScan(scanID string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	ctx, exists := sm.activeScans[scanID]
	if !exists {
		return fmt.Errorf("扫描 %s 不存在或已完成", scanID)
	}

	// 发送取消信号
	select {
	case ctx.Cancel <- true:
		ctx.Status = "已取消"
		return nil
	default:
		return fmt.Errorf("无法取消扫描 %s", scanID)
	}
}

// GetActiveScans 获取活动扫描
func (sm *ScanManager) GetActiveScans() map[string]*ScanContext {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	// 复制一份返回，避免并发问题
	result := make(map[string]*ScanContext)
	for k, v := range sm.activeScans {
		result[k] = &ScanContext{
			ScanID:    v.ScanID,
			Status:    v.Status,
			StartTime: v.StartTime,
			Progress:  v.Progress,
		}
	}

	return result
}

// GetScanStatus 获取扫描状态
func (sm *ScanManager) GetScanStatus(scanID string) (*ScanContext, error) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	ctx, exists := sm.activeScans[scanID]
	if !exists {
		return nil, fmt.Errorf("扫描 %s 不存在", scanID)
	}

	return &ScanContext{
		ScanID:    ctx.ScanID,
		Status:    ctx.Status,
		StartTime: ctx.StartTime,
		Progress:  ctx.Progress,
	}, nil
}

// GetStatistics 获取扫描统计信息
func (sm *ScanManager) GetStatistics() map[string]interface{} {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	stats := map[string]interface{}{
		"activeScans":   len(sm.activeScans),
		"singleScanner": sm.singleScanner.GetScanInfo(),
		"batchScanner":  sm.batchScanner.GetScanInfo(),
	}

	// 统计各状态的扫描数量
	statusCount := make(map[string]int)
	for _, ctx := range sm.activeScans {
		statusCount[ctx.Status]++
	}
	stats["statusCount"] = statusCount

	return stats
}

// Close 关闭扫描管理器
func (sm *ScanManager) Close() {
	// 取消所有活动扫描
	sm.mutex.Lock()
	for scanID := range sm.activeScans {
		if ctx := sm.activeScans[scanID]; ctx != nil {
			select {
			case ctx.Cancel <- true:
			default:
			}
		}
	}
	sm.mutex.Unlock()

	// 关闭进度通道
	close(sm.progressChan)
}
