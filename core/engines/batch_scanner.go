package engines

import (
	"fmt"
	"sync"
	"time"

	"lcheck/data"
)

// SingleScannerInterface 单主机扫描器接口 - 避免循环依赖
type SingleScannerInterface interface {
	ScanHost(host data.HostInfo) (*data.ScanResult, error)
	ScanHostWithProgress(host data.HostInfo, progressCallback func(float64)) (*data.ScanResult, error)
	ValidateHost(host data.HostInfo) error
	SetTimeout(timeout time.Duration)
	GetTimeout() time.Duration
	IsScanning() bool
	Stop() error
	GetScanInfo() map[string]interface{}
}

// BatchScanner 批量扫描器 - 专门负责批量主机扫描
type BatchScanner struct {
	singleScanner  SingleScannerInterface
	maxConcurrency int
}

// NewBatchScanner 创建批量扫描器
func NewBatchScanner(singleScanner SingleScannerInterface, maxConcurrency int) *BatchScanner {
	if maxConcurrency <= 0 {
		maxConcurrency = 5 // 默认并发数
	}

	return &BatchScanner{
		singleScanner:  singleScanner,
		maxConcurrency: maxConcurrency,
	}
}

// ScanHosts 批量扫描主机列表（接口方法）
func (bs *BatchScanner) ScanHosts(hosts []data.HostInfo) ([]data.ScanResult, error) {
	batchResult, err := bs.ScanHostsWithProgressCallback(hosts, bs.maxConcurrency, nil)
	if err != nil {
		return nil, err
	}
	return batchResult.Results, nil
}

// ScanHostsWithConcurrency 指定并发数的批量扫描
func (bs *BatchScanner) ScanHostsWithConcurrency(hosts []data.HostInfo, concurrency int) ([]data.ScanResult, error) {
	batchResult, err := bs.ScanHostsWithProgressCallback(hosts, concurrency, nil)
	if err != nil {
		return nil, err
	}
	return batchResult.Results, nil
}

// ScanHostsWithProgress 带进度回调的批量扫描（接口方法）
func (bs *BatchScanner) ScanHostsWithProgress(hosts []data.HostInfo, progressCallback func(int, int)) ([]data.ScanResult, error) {
	// 将func(int, int)转换为func(data.ScanProgress)
	var convertedCallback func(data.ScanProgress)
	if progressCallback != nil {
		convertedCallback = func(progress data.ScanProgress) {
			// 简化处理，只传递完成数和总数
			progressCallback(progress.Progress, 100)
		}
	}

	batchResult, err := bs.ScanHostsWithProgressCallback(hosts, bs.maxConcurrency, convertedCallback)
	if err != nil {
		return nil, err
	}
	return batchResult.Results, nil
}

// ScanHostsWithProgressCallback 带进度回调的批量扫描主机列表
func (bs *BatchScanner) ScanHostsWithProgressCallback(hosts []data.HostInfo, concurrency int, progressCallback func(data.ScanProgress)) (*data.BatchScanResult, error) {
	if concurrency <= 0 || concurrency > bs.maxConcurrency {
		concurrency = bs.maxConcurrency
	}

	batchResult := &data.BatchScanResult{
		ID:             fmt.Sprintf("batch_%d", time.Now().Unix()),
		StartTime:      time.Now(),
		Status:         "运行中",
		Results:        make([]data.ScanResult, 0, len(hosts)),
		TotalHosts:     len(hosts),
		CompletedHosts: 0,
		FailedHosts:    0,
	}

	// 创建工作池
	hostChan := make(chan data.HostInfo, len(hosts))
	resultChan := make(chan data.ScanResult, len(hosts))

	// 发送主机到通道
	for _, host := range hosts {
		hostChan <- host
	}
	close(hostChan)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go bs.scanWorker(&wg, hostChan, resultChan, progressCallback)
	}

	// 等待所有工作完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	for result := range resultChan {
		batchResult.Results = append(batchResult.Results, result)

		// 更新统计
		batchResult.CompletedHosts++
		if result.Status == "已完成" {
			// TotalScore字段不存在，暂时跳过
		} else {
			batchResult.FailedHosts++
		}

		// 发送批量进度
		if progressCallback != nil {
			progress := int((float64(batchResult.CompletedHosts) / float64(batchResult.TotalHosts)) * 100)
			progressCallback(data.ScanProgress{
				HostID:   batchResult.ID,
				Progress: progress,
				Status:   "批量扫描进行中",
				Message:  fmt.Sprintf("已完成 %d/%d 主机", batchResult.CompletedHosts, batchResult.TotalHosts),
			})
		}
	}

	// 计算平均分数（暂时跳过，因为没有相关字段）

	// 设置最终状态
	if batchResult.FailedHosts == 0 {
		batchResult.Status = "已完成"
	} else if batchResult.FailedHosts == batchResult.TotalHosts {
		batchResult.Status = "失败"
	} else {
		batchResult.Status = "部分完成"
	}

	batchResult.EndTime = time.Now()
	batchResult.Duration = time.Since(batchResult.StartTime)
	return batchResult, nil
}

// scanWorker 扫描工作协程
func (bs *BatchScanner) scanWorker(wg *sync.WaitGroup, hostChan <-chan data.HostInfo, resultChan chan<- data.ScanResult, progressCallback func(data.ScanProgress)) {
	defer wg.Done()

	for host := range hostChan {
		// 扫描单个主机
		result, err := bs.singleScanner.ScanHostWithProgress(host, func(progress float64) {
			// 将float64进度转换为ScanProgress结构
			if progressCallback != nil {
				progressCallback(data.ScanProgress{
					HostID:   host.ID,
					HostName: host.Name,
					Progress: int(progress * 100),
					Status:   "扫描中",
					Message:  fmt.Sprintf("扫描进度: %.1f%%", progress*100),
				})
			}
		})
		if err != nil && result == nil {
			// 创建错误结果
			result = &data.ScanResult{
				HostID:       host.ID,
				HostName:     host.Name,
				Host:         host.Host,
				StartTime:    time.Now(),
				Status:       "失败",
				Error:        err.Error(),
				CheckResults: make([]data.BaselineCheckResult, 0),
				TotalScore:   0,
				Duration:     0,
			}
		}

		resultChan <- *result
	}
}

// ScanHostGroup 扫描主机组
func (bs *BatchScanner) ScanHostGroup(group data.HostGroup, hosts []data.HostInfo, concurrency int, progressCallback func(data.ScanProgress)) (*data.BatchScanResult, error) {
	// 过滤出属于该组的主机
	var groupHosts []data.HostInfo
	hostMap := make(map[string]data.HostInfo)

	for _, host := range hosts {
		hostMap[host.ID] = host
	}

	for _, groupHost := range group.Hosts {
		if host, exists := hostMap[groupHost.ID]; exists {
			groupHosts = append(groupHosts, host)
		}
	}

	if len(groupHosts) == 0 {
		return nil, fmt.Errorf("主机组 %s 中没有有效的主机", group.Name)
	}

	// 执行批量扫描
	result, err := bs.ScanHostsWithProgressCallback(groupHosts, concurrency, progressCallback)
	if err != nil {
		return nil, err
	}

	// 更新结果信息
	result.ID = fmt.Sprintf("group_%s_%d", group.ID, time.Now().Unix())
	result.GroupID = group.ID
	result.GroupName = group.Name

	return result, nil
}

// CancelScan 取消扫描（简化版本）
func (bs *BatchScanner) CancelScan(scanID string) error {
	// 在实际实现中，这里应该有取消机制
	// 目前返回不支持
	return fmt.Errorf("批量扫描取消功能暂未实现")
}

// GetScanInfo 获取扫描器信息
func (bs *BatchScanner) GetScanInfo() map[string]interface{} {
	singleInfo := bs.singleScanner.GetScanInfo()
	return map[string]interface{}{
		"type":           "batch_scanner",
		"maxConcurrency": bs.maxConcurrency,
		"singleScanner":  singleInfo,
	}
}

// ValidateHosts 验证主机列表
func (bs *BatchScanner) ValidateHosts(hosts []data.HostInfo) []error {
	var errors []error

	for _, host := range hosts {
		if err := bs.singleScanner.ValidateHost(host); err != nil {
			errors = append(errors, fmt.Errorf("主机 %s: %v", host.Name, err))
		}
	}

	return errors
}

// EstimateScanTime 估算扫描时间
func (bs *BatchScanner) EstimateScanTime(hostCount, concurrency int) time.Duration {
	if concurrency <= 0 {
		concurrency = bs.maxConcurrency
	}

	// 假设每个主机平均扫描时间为30秒
	avgScanTime := 30 * time.Second

	// 计算批次数
	batches := (hostCount + concurrency - 1) / concurrency

	return time.Duration(batches) * avgScanTime
}

// ========== 实现ScanEngineInterface接口 ==========

// ScanHost 扫描单个主机（委托给单主机扫描器）
func (bs *BatchScanner) ScanHost(host data.HostInfo) (*data.ScanResult, error) {
	return bs.singleScanner.ScanHost(host)
}

// IsScanning 检查是否正在扫描
func (bs *BatchScanner) IsScanning() bool {
	return bs.singleScanner.IsScanning()
}

// Stop 停止扫描
func (bs *BatchScanner) Stop() error {
	return bs.singleScanner.Stop()
}

// SetTimeout 设置超时时间
func (bs *BatchScanner) SetTimeout(timeout time.Duration) {
	bs.singleScanner.SetTimeout(timeout)
}

// GetTimeout 获取超时时间
func (bs *BatchScanner) GetTimeout() time.Duration {
	return bs.singleScanner.GetTimeout()
}

// SetMaxConcurrency 设置最大并发数
func (bs *BatchScanner) SetMaxConcurrency(maxConcurrency int) {
	bs.maxConcurrency = maxConcurrency
}

// GetMaxConcurrency 获取最大并发数
func (bs *BatchScanner) GetMaxConcurrency() int {
	return bs.maxConcurrency
}
