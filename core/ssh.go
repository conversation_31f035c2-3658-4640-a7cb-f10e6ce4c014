package core

import (
	"fmt"
	"time"

	"lcheck/core/ssh"
	"lcheck/data"
)

// SSHClient SSH客户端管理器 - 模块化门面，委托给专门的SSH模块
type SSHClient struct {
	connectionManager *ssh.ConnectionManager
	commandExecutor   *ssh.CommandExecutor
	fileOperations    *ssh.FileOperations
}

// NewSSHClient 创建SSH客户端 - 使用模块化架构
func NewSSHClient(timeout time.Duration) *SSHClient {
	// 创建SSH模块
	connectionManager := ssh.NewConnectionManager(timeout)
	commandExecutor := ssh.NewCommandExecutor(connectionManager, timeout)
	fileOperations := ssh.NewFileOperations(commandExecutor)

	return &SSHClient{
		connectionManager: connectionManager,
		commandExecutor:   commandExecutor,
		fileOperations:    fileOperations,
	}
}

// ========== 连接管理委托方法 ==========

// TestConnection 测试SSH连接 - 委托给连接管理器
func (c *SSHClient) TestConnection(host data.HostInfo) error {
	return c.connectionManager.TestConnection(host)
}

// ValidateHost 验证主机信息 - 委托给连接管理器
func (c *SSHClient) ValidateHost(host data.HostInfo) error {
	return c.connectionManager.ValidateHost(host)
}

// IsHostReachable 检查主机是否可达 - 委托给连接管理器
func (c *SSHClient) IsHostReachable(host data.HostInfo) bool {
	return c.connectionManager.IsHostReachable(host)
}

// GetConnectionInfo 获取连接信息 - 委托给连接管理器
func (c *SSHClient) GetConnectionInfo(host data.HostInfo) map[string]interface{} {
	return c.connectionManager.GetConnectionInfo(host)
}

// ========== 命令执行委托方法 ==========

// ExecuteCommand 执行单个命令 - 委托给命令执行器
func (c *SSHClient) ExecuteCommand(host data.HostInfo, command string) (string, error) {
	return c.commandExecutor.ExecuteCommand(host, command)
}

// ExecuteCommands 执行多个命令 - 委托给命令执行器
func (c *SSHClient) ExecuteCommands(host data.HostInfo, commands map[string]string) (map[string]string, error) {
	return c.commandExecutor.ExecuteCommands(host, commands)
}

// ExecuteCommandWithTimeout 执行命令（带超时） - 委托给命令执行器
func (c *SSHClient) ExecuteCommandWithTimeout(host data.HostInfo, command string, timeout time.Duration) (string, error) {
	return c.commandExecutor.ExecuteCommandWithTimeout(host, command, timeout)
}

// ExecuteScript 执行脚本 - 委托给命令执行器
func (c *SSHClient) ExecuteScript(host data.HostInfo, script string) (string, error) {
	return c.commandExecutor.ExecuteScript(host, script)
}

// ExecuteCommandAsRoot 以root权限执行命令 - 委托给命令执行器
func (c *SSHClient) ExecuteCommandAsRoot(host data.HostInfo, command, sudoPassword string) (string, error) {
	return c.commandExecutor.ExecuteCommandAsRoot(host, command, sudoPassword)
}

// ValidateCommand 验证命令 - 委托给命令执行器
func (c *SSHClient) ValidateCommand(command string) error {
	return c.commandExecutor.ValidateCommand(command)
}

// GetRunningProcesses 获取运行中的进程 - 委托给命令执行器
func (c *SSHClient) GetRunningProcesses(host data.HostInfo) (string, error) {
	return c.commandExecutor.GetRunningProcesses(host)
}

// GetSystemLoad 获取系统负载 - 委托给命令执行器
func (c *SSHClient) GetSystemLoad(host data.HostInfo) (string, error) {
	return c.commandExecutor.GetSystemLoad(host)
}

// GetDiskUsage 获取磁盘使用情况 - 委托给命令执行器
func (c *SSHClient) GetDiskUsage(host data.HostInfo) (string, error) {
	return c.commandExecutor.GetDiskUsage(host)
}

// GetMemoryUsage 获取内存使用情况 - 委托给命令执行器
func (c *SSHClient) GetMemoryUsage(host data.HostInfo) (string, error) {
	return c.commandExecutor.GetMemoryUsage(host)
}

// GetNetworkConnections 获取网络连接 - 委托给命令执行器
func (c *SSHClient) GetNetworkConnections(host data.HostInfo) (string, error) {
	return c.commandExecutor.GetNetworkConnections(host)
}

// ========== 文件操作委托方法 ==========

// FileExists 检查文件是否存在 - 委托给文件操作器
func (c *SSHClient) FileExists(host data.HostInfo, filePath string) (bool, error) {
	return c.fileOperations.FileExists(host, filePath)
}

// DirectoryExists 检查目录是否存在 - 委托给文件操作器
func (c *SSHClient) DirectoryExists(host data.HostInfo, dirPath string) (bool, error) {
	return c.fileOperations.DirectoryExists(host, dirPath)
}

// GetFileContent 获取文件内容 - 委托给文件操作器
func (c *SSHClient) GetFileContent(host data.HostInfo, filePath string) (string, error) {
	return c.fileOperations.GetFileContent(host, filePath)
}

// GetFilePermissions 获取文件权限 - 委托给文件操作器
func (c *SSHClient) GetFilePermissions(host data.HostInfo, filePath string) (string, error) {
	return c.fileOperations.GetFilePermissions(host, filePath)
}

// GetFileOwner 获取文件所有者 - 委托给文件操作器
func (c *SSHClient) GetFileOwner(host data.HostInfo, filePath string) (string, error) {
	return c.fileOperations.GetFileOwner(host, filePath)
}

// CreateFile 创建文件 - 委托给文件操作器
func (c *SSHClient) CreateFile(host data.HostInfo, filePath, content string) error {
	return c.fileOperations.CreateFile(host, filePath, content)
}

// DeleteFile 删除文件 - 委托给文件操作器
func (c *SSHClient) DeleteFile(host data.HostInfo, filePath string) error {
	return c.fileOperations.DeleteFile(host, filePath)
}

// ListDirectory 列出目录内容 - 委托给文件操作器
func (c *SSHClient) ListDirectory(host data.HostInfo, dirPath string) ([]string, error) {
	return c.fileOperations.ListDirectory(host, dirPath)
}

// FindFiles 查找文件 - 委托给文件操作器
func (c *SSHClient) FindFiles(host data.HostInfo, searchPath, pattern string) ([]string, error) {
	return c.fileOperations.FindFiles(host, searchPath, pattern)
}

// SearchInFile 在文件中搜索内容 - 委托给文件操作器
func (c *SSHClient) SearchInFile(host data.HostInfo, filePath, pattern string) ([]string, error) {
	return c.fileOperations.SearchInFile(host, filePath, pattern)
}

// GetFileInfo 获取文件详细信息 - 委托给文件操作器
func (c *SSHClient) GetFileInfo(host data.HostInfo, filePath string) (map[string]string, error) {
	return c.fileOperations.GetFileInfo(host, filePath)
}

// ========== 配置和管理方法 ==========

// SetTimeout 设置超时时间
func (c *SSHClient) SetTimeout(timeout time.Duration) {
	c.connectionManager.SetTimeout(timeout)
	c.commandExecutor.SetTimeout(timeout)
}

// GetTimeout 获取超时时间
func (c *SSHClient) GetTimeout() time.Duration {
	return c.connectionManager.GetTimeout()
}

// GetSupportedAuthTypes 获取支持的认证类型 - 委托给连接管理器
func (c *SSHClient) GetSupportedAuthTypes() []string {
	return c.connectionManager.GetSupportedAuthTypes()
}

// GetSSHInfo 获取SSH客户端信息
func (c *SSHClient) GetSSHInfo() map[string]interface{} {
	return map[string]interface{}{
		"timeout":            c.GetTimeout().String(),
		"supportedAuthTypes": c.GetSupportedAuthTypes(),
		"modules": map[string]string{
			"connectionManager": "SSH连接管理器",
			"commandExecutor":   "SSH命令执行器",
			"fileOperations":    "SSH文件操作器",
		},
	}
}

// ========== 批量操作方法 ==========

// TestMultipleConnections 测试多个主机连接
func (c *SSHClient) TestMultipleConnections(hosts []data.HostInfo) map[string]error {
	results := make(map[string]error)

	for _, host := range hosts {
		err := c.TestConnection(host)
		results[host.ID] = err
	}

	return results
}

// ExecuteCommandOnMultipleHosts 在多个主机上执行命令
func (c *SSHClient) ExecuteCommandOnMultipleHosts(hosts []data.HostInfo, command string) map[string]string {
	results := make(map[string]string)

	for _, host := range hosts {
		output, err := c.ExecuteCommand(host, command)
		if err != nil {
			results[host.ID] = fmt.Sprintf("错误: %v", err)
		} else {
			results[host.ID] = output
		}
	}

	return results
}

// Close 关闭SSH客户端（清理资源）
func (c *SSHClient) Close() {
	// 在实际实现中，这里应该清理连接池等资源
	// 目前为简化实现，暂时为空
}
