package core

import (
	"time"

	"lcheck/data"
)

// SSHClientInterface SSH客户端接口 - 解决循环依赖
type SSHClientInterface interface {
	// 连接管理
	TestConnection(host data.HostInfo) error
	ValidateHost(host data.HostInfo) error
	IsHostReachable(host data.HostInfo) bool
	GetConnectionInfo(host data.HostInfo) map[string]interface{}

	// 命令执行
	ExecuteCommand(host data.HostInfo, command string) (string, error)
	ExecuteCommands(host data.HostInfo, commands map[string]string) (map[string]string, error)
	ExecuteCommandWithTimeout(host data.HostInfo, command string, timeout time.Duration) (string, error)
	ExecuteScript(host data.HostInfo, script string) (string, error)
	ExecuteCommandAsRoot(host data.HostInfo, command, sudoPassword string) (string, error)

	// 文件操作
	FileExists(host data.HostInfo, filePath string) (bool, error)
	DirectoryExists(host data.HostInfo, dirPath string) (bool, error)
	GetFileContent(host data.HostInfo, filePath string) (string, error)
	GetFilePermissions(host data.HostInfo, filePath string) (string, error)
	GetFileOwner(host data.HostInfo, filePath string) (string, error)
	CreateFile(host data.HostInfo, filePath, content string) error
	DeleteFile(host data.HostInfo, filePath string) error
	ListDirectory(host data.HostInfo, dirPath string) ([]string, error)
	FindFiles(host data.HostInfo, searchPath, pattern string) ([]string, error)
	SearchInFile(host data.HostInfo, filePath, pattern string) ([]string, error)
	GetFileInfo(host data.HostInfo, filePath string) (map[string]string, error)

	// 系统信息
	GetRunningProcesses(host data.HostInfo) (string, error)
	GetSystemLoad(host data.HostInfo) (string, error)
	GetDiskUsage(host data.HostInfo) (string, error)
	GetMemoryUsage(host data.HostInfo) (string, error)
	GetNetworkConnections(host data.HostInfo) (string, error)

	// 配置管理
	SetTimeout(timeout time.Duration)
	GetTimeout() time.Duration
}

// BaselineCheckerInterface 基线检查器接口 - 解决循环依赖
type BaselineCheckerInterface interface {
	// 基线检查
	CheckHost(host data.HostInfo) (*data.ScanResult, error)
	CheckHostWithContext(host data.HostInfo, ctx interface{}) (*data.ScanResult, error)

	// 检查器管理
	GetAvailableCheckers() []string
	EnableChecker(checkerName string) error
	DisableChecker(checkerName string) error
	IsCheckerEnabled(checkerName string) bool

	// 配置管理
	SetTimeout(timeout time.Duration)
	GetTimeout() time.Duration
}

// ScanEngineInterface 扫描引擎接口 - 统一扫描引擎接口
type ScanEngineInterface interface {
	// 扫描操作
	ScanHost(host data.HostInfo) (*data.ScanResult, error)
	ScanHosts(hosts []data.HostInfo) ([]data.ScanResult, error)

	// 状态管理
	IsScanning() bool
	Stop() error

	// 配置管理
	SetTimeout(timeout time.Duration)
	GetTimeout() time.Duration
}

// SingleScannerInterface 单主机扫描器接口
type SingleScannerInterface interface {
	ScanEngineInterface

	// 单主机特定方法
	ScanHostWithProgress(host data.HostInfo, progressCallback func(float64)) (*data.ScanResult, error)
}

// BatchScannerInterface 批量扫描器接口
type BatchScannerInterface interface {
	ScanEngineInterface

	// 批量扫描特定方法
	ScanHostsWithConcurrency(hosts []data.HostInfo, concurrency int) ([]data.ScanResult, error)
	ScanHostsWithProgress(hosts []data.HostInfo, progressCallback func(int, int)) ([]data.ScanResult, error)

	// 并发控制
	SetMaxConcurrency(maxConcurrency int)
	GetMaxConcurrency() int
}

// ScanManagerInterface 扫描管理器接口
type ScanManagerInterface interface {
	// 扫描管理
	StartScan(hosts []data.HostInfo, options ScanOptions) (*ScanSession, error)
	StopScan(sessionID string) error
	GetScanStatus(sessionID string) (*ScanStatus, error)

	// 会话管理
	GetActiveSessions() []*ScanSession
	CleanupCompletedSessions() error
}

// ScanOptions 扫描选项
type ScanOptions struct {
	Concurrency      int           `json:"concurrency"`
	Timeout          time.Duration `json:"timeout"`
	EnabledCheckers  []string      `json:"enabledCheckers"`
	ProgressCallback func(float64) `json:"-"`
}

// ScanSession 扫描会话
type ScanSession struct {
	ID        string            `json:"id"`
	Hosts     []data.HostInfo   `json:"hosts"`
	Options   ScanOptions       `json:"options"`
	Status    string            `json:"status"`
	Progress  float64           `json:"progress"`
	Results   []data.ScanResult `json:"results"`
	StartTime time.Time         `json:"startTime"`
	EndTime   *time.Time        `json:"endTime,omitempty"`
	Error     string            `json:"error,omitempty"`
}

// ScanStatus 扫描状态
type ScanStatus struct {
	SessionID      string        `json:"sessionId"`
	Status         string        `json:"status"`
	Progress       float64       `json:"progress"`
	CompletedHosts int           `json:"completedHosts"`
	TotalHosts     int           `json:"totalHosts"`
	ElapsedTime    time.Duration `json:"elapsedTime"`
	EstimatedTime  time.Duration `json:"estimatedTime"`
}
