package checkers

import (
	"fmt"
	"strings"

	"lcheck/data"
)

// SystemCollector 系统信息收集器 - 专门负责系统信息收集
type SystemCollector struct {
	name string
}

// NewSystemCollector 创建系统信息收集器
func NewSystemCollector() *SystemCollector {
	return &SystemCollector{
		name: "系统信息收集器",
	}
}

// GetName 获取收集器名称
func (sc *SystemCollector) GetName() string {
	return sc.name
}

// GetCollectors 获取系统信息收集项
func (sc *SystemCollector) GetCollectors() []data.BaselineCheck {
	return []data.BaselineCheck{
		{
			ID:          "SYS_INFO",
			Name:        "服务器基本信息收集",
			Category:    "信息收集",
			Description: "收集服务器基本信息",
			Risk:        "信息",
			CheckFunc: func(client interface{}, host data.HostInfo) data.BaselineCheckResult {
				return sc.collectSystemInfo(client.(SSHExecutor), host)
			},
		},
	}
}

// Collect 执行特定信息收集
func (sc *SystemCollector) Collect(sshClient SSHExecutor, host data.HostInfo, collectorID string) (*data.BaselineCheckResult, error) {
	collectors := sc.GetCollectors()
	for _, collector := range collectors {
		if collector.ID == collectorID {
			result := collector.CheckFunc(sshClient, host)
			result.CheckName = collector.Name
			result.Category = collector.Category
			result.Description = collector.Description
			result.Risk = collector.Risk
			return &result, nil
		}
	}
	return nil, fmt.Errorf("信息收集项 %s 不存在", collectorID)
}

// CollectAll 执行所有信息收集
func (sc *SystemCollector) CollectAll(sshClient SSHExecutor, host data.HostInfo) ([]data.BaselineCheckResult, error) {
	var results []data.BaselineCheckResult
	collectors := sc.GetCollectors()

	for _, collector := range collectors {
		result := collector.CheckFunc(sshClient, host)
		result.CheckName = collector.Name
		result.Category = collector.Category
		result.Description = collector.Description
		result.Risk = collector.Risk
		results = append(results, result)
	}

	return results, nil
}

// collectSystemInfo 收集服务器基本信息
func (sc *SystemCollector) collectSystemInfo(sshClient SSHExecutor, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		ID:      "SYS_INFO",
		Command: "echo '=== HOSTNAME ==='; hostname; echo '=== ARCH ==='; uname -m; echo '=== CPU ==='; lscpu | grep 'Model name' | head -1; echo '=== CPUCOUNT ==='; nproc; echo '=== MEMORY ==='; free -g | grep Mem; echo '=== HARDWARE ==='; dmidecode -s system-product-name 2>/dev/null || echo 'Unknown'; echo '=== OS ==='; cat /etc/os-release | grep PRETTY_NAME; echo '=== NETWORK ==='; ping -c 1 ******* >/dev/null 2>&1 && echo 'Connected' || echo 'Disconnected'",
	}

	output, err := sshClient.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output
	result.Status = "信息收集"
	result.Score = 100

	if result.Metadata == nil {
		result.Metadata = make(map[string]string)
	}

	// 解析各项信息
	sections := strings.Split(output, "=== ")
	for _, section := range sections {
		if strings.Contains(section, "HOSTNAME") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["hostname"] = strings.TrimSpace(lines[1])
			}
		} else if strings.Contains(section, "ARCH") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["arch"] = strings.TrimSpace(lines[1])
			}
		} else if strings.Contains(section, "CPU") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				cpuInfo := strings.TrimSpace(lines[1])
				if strings.Contains(cpuInfo, "Model name:") {
					parts := strings.Split(cpuInfo, ":")
					if len(parts) > 1 {
						result.Metadata["cpuModel"] = strings.TrimSpace(parts[1])
					}
				}
			}
		} else if strings.Contains(section, "CPUCOUNT") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["cpuCount"] = strings.TrimSpace(lines[1])
				result.Metadata["cpuCores"] = strings.TrimSpace(lines[1])
			}
		} else if strings.Contains(section, "MEMORY") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				memInfo := strings.TrimSpace(lines[1])
				fields := strings.Fields(memInfo)
				if len(fields) > 1 {
					result.Metadata["memory"] = fields[1] + "G"
				}
			}
		} else if strings.Contains(section, "HARDWARE") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["hardware"] = strings.TrimSpace(lines[1])
			}
		} else if strings.Contains(section, "OS") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				osInfo := strings.TrimSpace(lines[1])
				if strings.Contains(osInfo, "PRETTY_NAME=") {
					parts := strings.Split(osInfo, "=")
					if len(parts) > 1 {
						result.Metadata["osVersion"] = strings.Trim(parts[1], "\"")
					}
				}
			}
		} else if strings.Contains(section, "NETWORK") {
			lines := strings.Split(section, "\n")
			if len(lines) > 1 {
				result.Metadata["networkTest"] = strings.TrimSpace(lines[1])
			}
		}
	}

	result.Details = "服务器基本信息收集完成"
	return result
}
