package services

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"lcheck/data"
)

// ExportOptions 导出选项
type ExportOptions struct {
	Format     string // "html", "csv", "json"
	Content    string // "全部内容", "仅失败项目", "仅摘要信息"
	IncludeRaw bool   // 是否包含原始输出
}

// ExportService 导出服务
type ExportService struct {
	storage data.Storage
}

// NewExportService 创建导出服务
func NewExportService(storage data.Storage) *ExportService {
	return &ExportService{
		storage: storage,
	}
}

// ExportTask 导出任务结果
func (es *ExportService) ExportTask(task *data.ScanTask, options ExportOptions) (string, error) {
	switch options.Format {
	case "html":
		return es.exportToHTML(task, options)
	case "csv":
		return es.exportToCSV(task, options)
	case "json":
		return es.exportToJSON(task, options)
	default:
		return "", fmt.Errorf("不支持的导出格式: %s", options.Format)
	}
}

// SaveExportFile 保存导出文件
func (es *ExportService) SaveExportFile(filePath, content string) error {
	// 确保exports目录存在
	exportDir := filepath.Dir(filePath)
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(filePath, []byte(content), 0644); err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	return nil
}

// GenerateFileName 生成导出文件名
func (es *ExportService) GenerateFileName(task *data.ScanTask, format string) string {
	timestamp := time.Now().Format("20060102_150405")
	taskName := strings.ReplaceAll(task.Name, " ", "_")
	return fmt.Sprintf("%s_%s.%s", taskName, timestamp, format)
}

// FormatFileSize 格式化文件大小
func (es *ExportService) FormatFileSize(size int) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// findCheckResult 查找特定ID的检查结果
func (es *ExportService) findCheckResult(checkResults []data.BaselineCheckResult, checkID string) *data.BaselineCheckResult {
	for i := range checkResults {
		if checkResults[i].ID == checkID {
			return &checkResults[i]
		}
	}
	return nil
}

// escapeCSV 转义CSV字段中的特殊字符
func (es *ExportService) escapeCSV(field string) string {
	if strings.Contains(field, ",") || strings.Contains(field, "\"") || strings.Contains(field, "\n") {
		field = strings.ReplaceAll(field, "\"", "\"\"")
		field = "\"" + field + "\""
	}
	return field
}

// exportToHTML 导出为HTML格式
func (es *ExportService) exportToHTML(task *data.ScanTask, options ExportOptions) (string, error) {
	htmlService := NewHTMLTemplateService()
	securityService := NewSecurityTableService()

	var buffer strings.Builder

	// HTML头部
	buffer.WriteString(htmlService.GenerateHTMLHeader())

	// 任务摘要
	buffer.WriteString(htmlService.GenerateTaskSummary(task))

	// 主机结果
	for i, result := range task.Results {
		// 主机头部
		buffer.WriteString(htmlService.GenerateHostHeader(i, &result))

		// 得分信息
		buffer.WriteString(htmlService.GenerateScoreInfo(&result))

		// 安全检查表格 - 按模板格式：实际结果 + 建议配置
		buffer.WriteString(securityService.GeneratePasswordPolicyTable(result.CheckResults))
		buffer.WriteString(securityService.GenerateSSHConfigTable(result.CheckResults))
		buffer.WriteString(securityService.GeneratePortStatusTable(result.CheckResults))

		// 检查结果表格
		buffer.WriteString(htmlService.GenerateCheckResultsTable(result.CheckResults, options.Content))

		buffer.WriteString(`
            </div>
        </div>
`)
	}

	// HTML尾部
	buffer.WriteString(htmlService.GenerateHTMLFooter())

	return buffer.String(), nil
}

// exportToCSV 导出为CSV格式
func (es *ExportService) exportToCSV(task *data.ScanTask, options ExportOptions) (string, error) {
	var buffer strings.Builder

	// CSV头部
	buffer.WriteString("主机名,主机地址,检查项,类别,结果,风险等级,得分,详情,解决方案\n")

	// 数据行
	for _, result := range task.Results {
		for _, check := range result.CheckResults {
			if options.Content == "仅失败项目" && check.Status == "通过" {
				continue
			}

			buffer.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,%s,%d,%s,%s\n",
				es.escapeCSV(result.HostName),
				es.escapeCSV(result.Host),
				es.escapeCSV(check.CheckName),
				es.escapeCSV(check.Category),
				es.escapeCSV(check.Status),
				es.escapeCSV(check.Risk),
				check.Score,
				es.escapeCSV(check.Details),
				es.escapeCSV(check.Solution)))
		}
	}

	return buffer.String(), nil
}

// exportToJSON 导出为JSON格式
func (es *ExportService) exportToJSON(task *data.ScanTask, options ExportOptions) (string, error) {
	// 这里可以实现JSON导出逻辑
	return fmt.Sprintf(`{"task": "%s", "status": "not implemented"}`, task.Name), nil
}

// ExportHostsToCSV 导出主机信息为CSV格式（只包含核心连接信息）
func (es *ExportService) ExportHostsToCSV(hosts []data.HostInfo) string {
	var buffer strings.Builder

	// CSV头部 - 只包含核心连接信息
	buffer.WriteString("主机名,主机地址,端口,用户名,密码\n")

	// 数据行
	for _, host := range hosts {
		buffer.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s\n",
			es.escapeCSV(host.Name),
			es.escapeCSV(host.Host),
			es.escapeCSV(host.Port),
			es.escapeCSV(host.Username),
			es.escapeCSV(host.Password)))
	}

	return buffer.String()
}

// ExportGroupsToCSV 导出主机组信息为CSV格式（只包含核心信息，支持批量导入）
func (es *ExportService) ExportGroupsToCSV(groups []data.HostGroup) string {
	var buffer strings.Builder

	// CSV头部 - 只包含核心信息
	buffer.WriteString("主机组名,主机组描述,主机名,主机地址,端口,用户名,密码\n")

	// 数据行 - 每台主机一行，包含主机组信息
	for _, group := range groups {
		// 如果主机组没有主机，也要导出主机组信息
		if len(group.Hosts) == 0 {
			buffer.WriteString(fmt.Sprintf("%s,%s,,,,,\n",
				es.escapeCSV(group.Name),
				es.escapeCSV(group.Description)))
		} else {
			// 为每台主机生成一行，包含主机组信息
			for _, host := range group.Hosts {
				buffer.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,%s,%s\n",
					es.escapeCSV(group.Name),
					es.escapeCSV(group.Description),
					es.escapeCSV(host.Name),
					es.escapeCSV(host.Host),
					es.escapeCSV(host.Port),
					es.escapeCSV(host.Username),
					es.escapeCSV(host.Password)))
			}
		}
	}

	return buffer.String()
}

// GenerateHostFileName 生成主机导出文件名
func (es *ExportService) GenerateHostFileName() string {
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("hosts_export_%s.csv", timestamp)
}

// GenerateGroupFileName 生成主机组导出文件名
func (es *ExportService) GenerateGroupFileName() string {
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("groups_export_%s.csv", timestamp)
}

// ImportHostsFromCSV 从CSV格式导入主机信息
func (es *ExportService) ImportHostsFromCSV(csvContent string) ([]data.HostInfo, error) {
	lines := strings.Split(csvContent, "\n")
	if len(lines) < 2 {
		return nil, fmt.Errorf("CSV文件格式错误：至少需要标题行和一行数据")
	}

	// 验证标题行
	expectedHeader := "主机名,主机地址,端口,用户名,密码"
	actualHeader := strings.TrimSpace(lines[0])
	actualHeader = strings.ReplaceAll(actualHeader, "\r", "") // 移除Windows行尾符
	if actualHeader != expectedHeader {
		return nil, fmt.Errorf("CSV文件格式错误：标题行应为 '%s'，实际为 '%s'", expectedHeader, actualHeader)
	}

	var hosts []data.HostInfo
	for i, line := range lines[1:] {
		line = strings.TrimSpace(line)
		if line == "" {
			continue // 跳过空行
		}

		// 解析CSV行
		fields := es.parseCSVLine(line)
		if len(fields) != 5 {
			return nil, fmt.Errorf("第%d行格式错误：应包含5个字段，实际%d个", i+2, len(fields))
		}

		// 创建主机信息
		host := data.HostInfo{
			ID:       "", // 导入时会重新生成ID
			Name:     fields[0],
			Host:     fields[1],
			Port:     fields[2],
			Username: fields[3],
			Password: fields[4],
		}

		hosts = append(hosts, host)
	}

	return hosts, nil
}

// ImportGroupsFromCSV 从CSV格式导入主机组信息（简化格式）
func (es *ExportService) ImportGroupsFromCSV(csvContent string) ([]data.HostGroup, error) {
	lines := strings.Split(csvContent, "\n")
	if len(lines) < 2 {
		return nil, fmt.Errorf("CSV文件格式错误：至少需要标题行和一行数据")
	}

	// 验证标题行
	expectedHeader := "主机组名,主机组描述,主机名,主机地址,端口,用户名,密码"
	actualHeader := strings.TrimSpace(lines[0])
	actualHeader = strings.ReplaceAll(actualHeader, "\r", "") // 移除Windows行尾符
	if actualHeader != expectedHeader {
		return nil, fmt.Errorf("CSV文件格式错误：标题行应为 '%s'，实际为 '%s'", expectedHeader, actualHeader)
	}

	// 使用map来收集主机组数据
	groupMap := make(map[string]*data.HostGroup)

	for i, line := range lines[1:] {
		line = strings.TrimSpace(line)
		if line == "" {
			continue // 跳过空行
		}

		// 解析CSV行
		fields := es.parseCSVLine(line)
		if len(fields) != 7 {
			return nil, fmt.Errorf("第%d行格式错误：应包含7个字段，实际%d个", i+2, len(fields))
		}

		groupName := fields[0]

		// 如果主机组不存在，创建新的主机组
		if _, exists := groupMap[groupName]; !exists {
			groupMap[groupName] = &data.HostGroup{
				ID:          "", // 导入时会重新生成ID
				Name:        groupName,
				Description: fields[1],
				Hosts:       []data.HostInfo{},
			}
		}

		// 如果有主机信息，添加到主机组中
		if fields[2] != "" { // 主机名不为空
			// 创建主机信息
			host := data.HostInfo{
				ID:       "", // 导入时会重新生成ID
				Name:     fields[2],
				Host:     fields[3],
				Port:     fields[4],
				Username: fields[5],
				Password: fields[6],
			}

			// 添加主机到主机组
			groupMap[groupName].Hosts = append(groupMap[groupName].Hosts, host)
		}
	}

	// 转换map为slice
	var groups []data.HostGroup
	for _, group := range groupMap {
		groups = append(groups, *group)
	}

	return groups, nil
}

// parseCSVLine 解析CSV行，处理引号和转义
func (es *ExportService) parseCSVLine(line string) []string {
	var fields []string
	var current strings.Builder
	inQuotes := false

	for i, char := range line {
		switch char {
		case '"':
			if inQuotes && i+1 < len(line) && line[i+1] == '"' {
				// 双引号转义
				current.WriteRune('"')
				i++ // 跳过下一个引号
			} else {
				inQuotes = !inQuotes
			}
		case ',':
			if inQuotes {
				current.WriteRune(char)
			} else {
				fields = append(fields, current.String())
				current.Reset()
			}
		default:
			current.WriteRune(char)
		}
	}

	// 添加最后一个字段
	fields = append(fields, current.String())

	return fields
}
