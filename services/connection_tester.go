package services

import (
	"fmt"
	"sync"
	"time"

	"lcheck/core"
	"lcheck/data"
)

// ConnectionTester 连接测试器 - 专门负责主机连接测试
type ConnectionTester struct {
	sshClient *core.SSHClient
}

// NewConnectionTester 创建连接测试器
func NewConnectionTester(sshClient *core.SSHClient) *ConnectionTester {
	return &ConnectionTester{
		sshClient: sshClient,
	}
}

// TestConnection 测试单个主机连接
func (ct *ConnectionTester) TestConnection(host data.HostInfo) *ConnectionTestResult {
	result := &ConnectionTestResult{
		HostID:    host.ID,
		HostName:  host.Name,
		HostIP:    host.Host,
		Port:      host.Port,
		StartTime: time.Now(),
		Status:    "测试中",
	}

	// 测试网络连通性
	if !ct.sshClient.IsHostReachable(host) {
		result.Status = "网络不通"
		result.Error = "无法连接到主机"
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 测试SSH连接
	err := ct.sshClient.TestConnection(host)
	result.Duration = time.Since(result.StartTime)

	if err != nil {
		result.Status = "连接失败"
		result.Error = err.Error()
	} else {
		result.Status = "连接成功"
		result.Error = ""
	}

	return result
}

// TestMultipleConnections 测试多个主机连接
func (ct *ConnectionTester) TestMultipleConnections(hosts []data.HostInfo, concurrency int) []*ConnectionTestResult {
	if concurrency <= 0 {
		concurrency = 5 // 默认并发数
	}

	results := make([]*ConnectionTestResult, len(hosts))
	hostChan := make(chan int, len(hosts))
	resultChan := make(chan struct {
		index  int
		result *ConnectionTestResult
	}, len(hosts))

	// 发送主机索引到通道
	for i := range hosts {
		hostChan <- i
	}
	close(hostChan)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for index := range hostChan {
				result := ct.TestConnection(hosts[index])
				resultChan <- struct {
					index  int
					result *ConnectionTestResult
				}{index, result}
			}
		}()
	}

	// 等待所有工作完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	for res := range resultChan {
		results[res.index] = res.result
	}

	return results
}

// TestGroupConnections 测试主机组连接
func (ct *ConnectionTester) TestGroupConnections(group data.HostGroup, hosts []data.HostInfo, concurrency int) *GroupTestResult {
	// 过滤出属于该组的主机
	var groupHosts []data.HostInfo
	hostMap := make(map[string]data.HostInfo)

	for _, host := range hosts {
		hostMap[host.ID] = host
	}

	for _, groupHost := range group.Hosts {
		if host, exists := hostMap[groupHost.ID]; exists {
			groupHosts = append(groupHosts, host)
		}
	}

	// 测试连接
	results := ct.TestMultipleConnections(groupHosts, concurrency)

	// 统计结果
	groupResult := &GroupTestResult{
		GroupID:      group.ID,
		GroupName:    group.Name,
		TotalHosts:   len(groupHosts),
		SuccessHosts: 0,
		FailedHosts:  0,
		TestResults:  results,
		StartTime:    time.Now(),
	}

	for _, result := range results {
		if result.Status == "连接成功" {
			groupResult.SuccessHosts++
		} else {
			groupResult.FailedHosts++
		}
	}

	groupResult.Duration = time.Since(groupResult.StartTime)
	return groupResult
}

// TestConnectionWithDetails 测试连接并获取详细信息
func (ct *ConnectionTester) TestConnectionWithDetails(host data.HostInfo) *DetailedConnectionTestResult {
	result := &DetailedConnectionTestResult{
		ConnectionTestResult: *ct.TestConnection(host),
		SystemInfo:           make(map[string]string),
	}

	// 如果连接成功，获取系统信息
	if result.Status == "连接成功" {
		ct.collectSystemInfo(host, result)
	}

	return result
}

// collectSystemInfo 收集系统信息
func (ct *ConnectionTester) collectSystemInfo(host data.HostInfo, result *DetailedConnectionTestResult) {
	// 获取系统信息
	commands := map[string]string{
		"hostname": "hostname",
		"uptime":   "uptime",
		"kernel":   "uname -r",
		"os":       "cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"'",
		"cpu":      "cat /proc/cpuinfo | grep 'model name' | head -1 | cut -d':' -f2 | xargs",
		"memory":   "free -h | grep Mem | awk '{print $2}'",
		"disk":     "df -h / | tail -1 | awk '{print $2}'",
	}

	systemInfo, err := ct.sshClient.ExecuteCommands(host, commands)
	if err != nil {
		result.SystemInfo["error"] = fmt.Sprintf("获取系统信息失败: %v", err)
		return
	}

	result.SystemInfo = systemInfo
}

// GetConnectionStatistics 获取连接统计信息
func (ct *ConnectionTester) GetConnectionStatistics(results []*ConnectionTestResult) *ConnectionStatistics {
	stats := &ConnectionStatistics{
		TotalTests:   len(results),
		SuccessCount: 0,
		FailureCount: 0,
		AverageTime:  0,
		MaxTime:      0,
		MinTime:      0,
		ErrorTypes:   make(map[string]int),
	}

	if len(results) == 0 {
		return stats
	}

	var totalDuration time.Duration
	stats.MinTime = results[0].Duration

	for _, result := range results {
		totalDuration += result.Duration

		if result.Duration > stats.MaxTime {
			stats.MaxTime = result.Duration
		}

		if result.Duration < stats.MinTime {
			stats.MinTime = result.Duration
		}

		if result.Status == "连接成功" {
			stats.SuccessCount++
		} else {
			stats.FailureCount++
			if result.Error != "" {
				stats.ErrorTypes[result.Error]++
			}
		}
	}

	stats.AverageTime = totalDuration / time.Duration(len(results))
	return stats
}

// ScheduleConnectionTest 定时连接测试
func (ct *ConnectionTester) ScheduleConnectionTest(hosts []data.HostInfo, interval time.Duration, callback func([]*ConnectionTestResult)) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for range ticker.C {
		results := ct.TestMultipleConnections(hosts, 5)
		if callback != nil {
			callback(results)
		}
	}
}

// ConnectionTestResult 连接测试结果
type ConnectionTestResult struct {
	HostID    string        `json:"hostId"`
	HostName  string        `json:"hostName"`
	HostIP    string        `json:"hostIp"`
	Port      string        `json:"port"`
	Status    string        `json:"status"`
	Error     string        `json:"error"`
	StartTime time.Time     `json:"startTime"`
	Duration  time.Duration `json:"duration"`
}

// DetailedConnectionTestResult 详细连接测试结果
type DetailedConnectionTestResult struct {
	ConnectionTestResult
	SystemInfo map[string]string `json:"systemInfo"`
}

// GroupTestResult 主机组测试结果
type GroupTestResult struct {
	GroupID      string                  `json:"groupId"`
	GroupName    string                  `json:"groupName"`
	TotalHosts   int                     `json:"totalHosts"`
	SuccessHosts int                     `json:"successHosts"`
	FailedHosts  int                     `json:"failedHosts"`
	TestResults  []*ConnectionTestResult `json:"testResults"`
	StartTime    time.Time               `json:"startTime"`
	Duration     time.Duration           `json:"duration"`
}

// ConnectionStatistics 连接统计信息
type ConnectionStatistics struct {
	TotalTests   int            `json:"totalTests"`
	SuccessCount int            `json:"successCount"`
	FailureCount int            `json:"failureCount"`
	AverageTime  time.Duration  `json:"averageTime"`
	MaxTime      time.Duration  `json:"maxTime"`
	MinTime      time.Duration  `json:"minTime"`
	ErrorTypes   map[string]int `json:"errorTypes"`
}
