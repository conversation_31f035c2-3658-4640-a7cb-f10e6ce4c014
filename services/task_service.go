package services

import (
	"fmt"
	"time"

	"lcheck/core"
	"lcheck/data"
)

// TaskService 任务管理服务
type TaskService struct {
	storage data.Storage
	scanner *core.Scanner
}

// NewTaskService 创建任务管理服务
func NewTaskService(storage data.Storage, scanner *core.Scanner) *TaskService {
	return &TaskService{
		storage: storage,
		scanner: scanner,
	}
}

// CreateTask 创建新任务
func (ts *TaskService) CreateTask(name string, hostIDs []string, concurrency int) (*data.ScanTask, error) {
	if name == "" {
		return nil, fmt.Errorf("任务名称不能为空")
	}

	if len(hostIDs) == 0 {
		return nil, fmt.Errorf("必须选择至少一个主机")
	}

	task := &data.ScanTask{
		ID:        fmt.Sprintf("task_%d", time.Now().Unix()),
		Name:      name,
		Status:    "待执行",
		HostIDs:   hostIDs,
		Progress:  0.0,
		CreatedAt: time.Now(),
		Results:   []data.ScanResult{},
		// 注意：当前数据模型没有Concurrency字段，暂时移除
	}

	return task, nil
}

// LoadTasks 加载所有任务
func (ts *TaskService) LoadTasks() ([]data.ScanTask, error) {
	return ts.storage.LoadTasks()
}

// SaveTasks 保存任务列表
func (ts *TaskService) SaveTasks(tasks []data.ScanTask) error {
	for _, task := range tasks {
		if err := ts.storage.SaveTask(task); err != nil {
			return err
		}
	}
	return nil
}

// SaveTask 保存单个任务
func (ts *TaskService) SaveTask(task *data.ScanTask) error {
	return ts.storage.SaveTask(*task)
}

// DeleteTask 删除任务
func (ts *TaskService) DeleteTask(taskID string) error {
	return ts.storage.DeleteTask(taskID)
}

// StartTask 启动任务
func (ts *TaskService) StartTask(task *data.ScanTask) error {
	if task.Status != "待执行" {
		return fmt.Errorf("只能启动待执行状态的任务")
	}

	// 更新任务状态
	task.Status = "运行中"
	task.Progress = 0.0
	now := time.Now()
	task.StartedAt = &now

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务状态失败: %v", err)
	}

	return nil
}

// StopTask 停止任务
func (ts *TaskService) StopTask(task *data.ScanTask) error {
	if task.Status != "运行中" {
		return fmt.Errorf("只能停止运行中的任务")
	}

	// 更新任务状态
	task.Status = "已停止"
	now := time.Now()
	task.CompletedAt = &now

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务状态失败: %v", err)
	}

	return nil
}

// CompleteTask 完成任务
func (ts *TaskService) CompleteTask(task *data.ScanTask) error {
	// 更新任务状态
	task.Status = "已完成"
	task.Progress = 1.0
	now := time.Now()
	task.CompletedAt = &now

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务状态失败: %v", err)
	}

	return nil
}

// UpdateTaskProgress 更新任务进度
func (ts *TaskService) UpdateTaskProgress(task *data.ScanTask, progress float64) error {
	task.Progress = progress

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务进度失败: %v", err)
	}

	return nil
}

// AddTaskResult 添加任务结果
func (ts *TaskService) AddTaskResult(task *data.ScanTask, result data.ScanResult) error {
	task.Results = append(task.Results, result)

	// 更新进度
	progress := float64(len(task.Results)) / float64(len(task.HostIDs))
	task.Progress = progress

	// 如果所有主机都完成了，标记任务为完成
	if len(task.Results) >= len(task.HostIDs) {
		return ts.CompleteTask(task)
	}

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务结果失败: %v", err)
	}

	return nil
}

// GetTaskStatistics 获取任务统计信息
func (ts *TaskService) GetTaskStatistics(task *data.ScanTask) map[string]interface{} {
	stats := map[string]interface{}{
		"totalHosts":     len(task.HostIDs),
		"completedHosts": len(task.Results),
		"progress":       task.Progress * 100,
		"status":         task.Status,
	}

	if task.StartedAt != nil {
		stats["startTime"] = task.StartedAt.Format("2006-01-02 15:04:05")
		if task.CompletedAt != nil {
			stats["duration"] = task.CompletedAt.Sub(*task.StartedAt).String()
		} else {
			stats["elapsed"] = time.Since(*task.StartedAt).String()
		}
	}

	if task.CompletedAt != nil {
		stats["completedTime"] = task.CompletedAt.Format("2006-01-02 15:04:05")
	}

	// 统计检查结果
	totalChecks := 0
	passedChecks := 0
	failedChecks := 0
	warningChecks := 0

	for _, result := range task.Results {
		totalChecks += result.TotalChecks
		passedChecks += result.PassedChecks
		failedChecks += result.FailedChecks
		warningChecks += result.WarningChecks
	}

	stats["totalChecks"] = totalChecks
	stats["passedChecks"] = passedChecks
	stats["failedChecks"] = failedChecks
	stats["warningChecks"] = warningChecks

	return stats
}

// ValidateTask 验证任务配置
func (ts *TaskService) ValidateTask(name string, hostIDs []string, concurrency int) error {
	if name == "" {
		return fmt.Errorf("任务名称不能为空")
	}

	if len(hostIDs) == 0 {
		return fmt.Errorf("必须选择至少一个主机")
	}

	if concurrency < 1 || concurrency > 10 {
		return fmt.Errorf("并发数必须在1-10之间")
	}

	// 验证主机是否存在
	hosts, err := ts.storage.LoadHosts()
	if err != nil {
		return fmt.Errorf("加载主机列表失败: %v", err)
	}

	hostMap := make(map[string]bool)
	for _, host := range hosts {
		hostMap[host.ID] = true
	}

	for _, hostID := range hostIDs {
		if !hostMap[hostID] {
			return fmt.Errorf("主机不存在: %s", hostID)
		}
	}

	return nil
}
