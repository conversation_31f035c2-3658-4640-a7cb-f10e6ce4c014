package services

import (
	"fmt"
	"strings"

	"lcheck/data"
	"lcheck/utils"
)

// GroupManager 主机组管理器 - 专门负责主机组的CRUD操作
type GroupManager struct {
	storage     data.Storage
	hostManager *HostManager
}

// NewGroupManager 创建主机组管理器
func NewGroupManager(storage data.Storage, hostManager *HostManager) *GroupManager {
	return &GroupManager{
		storage:     storage,
		hostManager: hostManager,
	}
}

// LoadGroups 加载所有主机组
func (gm *GroupManager) LoadGroups() ([]data.HostGroup, error) {
	return gm.storage.LoadGroups()
}

// SaveGroups 保存主机组列表
func (gm *GroupManager) SaveGroups(groups []data.HostGroup) error {
	for _, group := range groups {
		if err := gm.storage.SaveGroup(group); err != nil {
			return err
		}
	}
	return nil
}

// GetGroup 获取单个主机组信息
func (gm *GroupManager) GetGroup(groupID string) (*data.HostGroup, error) {
	return gm.storage.GetGroup(groupID)
}

// CreateGroup 创建新主机组（接收主机信息列表而不是主机ID）
func (gm *GroupManager) CreateGroup(name, description string, hosts []data.HostInfo) (*data.HostGroup, error) {
	// 验证输入
	if err := gm.validateGroupInput(name, hosts); err != nil {
		return nil, err
	}

	// 检查主机组名是否重复
	if err := gm.checkDuplicateGroupName(name); err != nil {
		return nil, err
	}

	// 为主机组内的主机生成ID
	for i := range hosts {
		if hosts[i].ID == "" {
			hosts[i].ID = utils.GenerateID("group_host")
		}
	}

	// 创建主机组
	group := &data.HostGroup{
		ID:          utils.GenerateID("group"),
		Name:        strings.TrimSpace(name),
		Description: strings.TrimSpace(description),
		Hosts:       hosts,
	}

	// 保存主机组
	if err := gm.storage.SaveGroup(*group); err != nil {
		return nil, fmt.Errorf("保存主机组失败: %v", err)
	}

	return group, nil
}

// UpdateGroup 更新主机组
func (gm *GroupManager) UpdateGroup(groupID, name, description string, hosts []data.HostInfo) error {
	// 验证输入
	if err := gm.validateGroupInput(name, hosts); err != nil {
		return err
	}

	// 获取现有主机组信息
	existingGroup, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return fmt.Errorf("获取主机组信息失败: %v", err)
	}

	// 为主机组内的主机生成ID
	for i := range hosts {
		if hosts[i].ID == "" {
			hosts[i].ID = utils.GenerateID("group_host")
		}
	}

	// 更新主机组信息
	existingGroup.Name = strings.TrimSpace(name)
	existingGroup.Description = strings.TrimSpace(description)
	existingGroup.Hosts = hosts

	// 保存更新后的主机组信息
	return gm.storage.SaveGroup(*existingGroup)
}

// DeleteGroup 删除主机组
func (gm *GroupManager) DeleteGroup(groupID string) error {
	return gm.storage.DeleteGroup(groupID)
}

// GetGroupHosts 获取主机组中的所有主机
func (gm *GroupManager) GetGroupHosts(groupID string) ([]data.HostInfo, error) {
	group, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取主机组失败: %v", err)
	}

	var hosts []data.HostInfo
	for _, groupHost := range group.Hosts {
		host, err := gm.storage.GetHost(groupHost.ID)
		if err != nil {
			// 跳过不存在的主机
			continue
		}
		hosts = append(hosts, *host)
	}

	return hosts, nil
}

// AddHostsToGroup 向主机组添加主机
func (gm *GroupManager) AddHostsToGroup(groupID string, hosts []data.HostInfo) error {
	// 获取主机组
	group, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return fmt.Errorf("获取主机组失败: %v", err)
	}

	// 验证新主机信息
	if err := gm.validateGroupHosts(hosts); err != nil {
		return err
	}

	// 为新主机生成ID并添加到主机组（去重）
	existingHosts := make(map[string]bool)
	for _, groupHost := range group.Hosts {
		existingHosts[strings.TrimSpace(groupHost.Name)] = true
	}

	for _, host := range hosts {
		hostName := strings.TrimSpace(host.Name)
		if !existingHosts[hostName] {
			if host.ID == "" {
				host.ID = utils.GenerateID("group_host")
			}
			group.Hosts = append(group.Hosts, host)
		}
	}

	// 保存更新后的主机组
	return gm.storage.SaveGroup(*group)
}

// RemoveHostsFromGroup 从主机组移除主机
func (gm *GroupManager) RemoveHostsFromGroup(groupID string, hostIDs []string) error {
	// 获取主机组
	group, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return fmt.Errorf("获取主机组失败: %v", err)
	}

	// 创建要移除的主机ID映射
	removeHosts := make(map[string]bool)
	for _, hostID := range hostIDs {
		removeHosts[hostID] = true
	}

	// 过滤掉要移除的主机
	var newHosts []data.HostInfo
	for _, groupHost := range group.Hosts {
		if !removeHosts[groupHost.ID] {
			newHosts = append(newHosts, groupHost)
		}
	}

	group.Hosts = newHosts

	// 保存更新后的主机组
	return gm.storage.SaveGroup(*group)
}

// DuplicateGroup 复制主机组
func (gm *GroupManager) DuplicateGroup(groupID string) (*data.HostGroup, error) {
	// 获取原主机组信息
	originalGroup, err := gm.storage.GetGroup(groupID)
	if err != nil {
		return nil, fmt.Errorf("获取原主机组信息失败: %v", err)
	}

	// 创建副本
	duplicatedGroup := *originalGroup
	duplicatedGroup.ID = utils.GenerateID("group")
	duplicatedGroup.Name = originalGroup.Name + "_副本"

	// 保存副本
	if err := gm.storage.SaveGroup(duplicatedGroup); err != nil {
		return nil, fmt.Errorf("保存主机组副本失败: %v", err)
	}

	return &duplicatedGroup, nil
}

// SearchGroups 搜索主机组
func (gm *GroupManager) SearchGroups(keyword string) ([]data.HostGroup, error) {
	groups, err := gm.LoadGroups()
	if err != nil {
		return nil, err
	}

	return utils.FilterGroups(groups, keyword), nil
}

// SortGroups 排序主机组
func (gm *GroupManager) SortGroups(groups []data.HostGroup, sortBy string) []data.HostGroup {
	switch sortBy {
	case "name":
		return utils.SortGroupsByName(groups)
	default:
		return groups
	}
}

// GetGroupStatistics 获取主机组统计信息
func (gm *GroupManager) GetGroupStatistics() (*GroupStatistics, error) {
	groups, err := gm.LoadGroups()
	if err != nil {
		return nil, err
	}

	stats := &GroupStatistics{
		TotalGroups:   len(groups),
		EmptyGroups:   0,
		LargestGroup:  0,
		SmallestGroup: 0,
		AverageSize:   0,
	}

	if len(groups) == 0 {
		return stats, nil
	}

	totalHosts := 0
	stats.SmallestGroup = len(groups[0].Hosts)

	for _, group := range groups {
		hostCount := len(group.Hosts)
		totalHosts += hostCount

		if hostCount == 0 {
			stats.EmptyGroups++
		}

		if hostCount > stats.LargestGroup {
			stats.LargestGroup = hostCount
		}

		if hostCount < stats.SmallestGroup {
			stats.SmallestGroup = hostCount
		}
	}

	stats.AverageSize = float64(totalHosts) / float64(len(groups))

	return stats, nil
}

// ValidateGroup 验证主机组配置
func (gm *GroupManager) ValidateGroup(group data.HostGroup) []string {
	return utils.ValidateHostGroup(group)
}

// BatchDeleteGroups 批量删除主机组
func (gm *GroupManager) BatchDeleteGroups(groupIDs []string) []error {
	var errors []error

	for _, groupID := range groupIDs {
		if err := gm.DeleteGroup(groupID); err != nil {
			errors = append(errors, fmt.Errorf("删除主机组 %s 失败: %v", groupID, err))
		}
	}

	return errors
}

// ExportGroups 导出主机组配置
func (gm *GroupManager) ExportGroups(groupIDs []string) ([]data.HostGroup, error) {
	var exportGroups []data.HostGroup

	for _, groupID := range groupIDs {
		group, err := gm.storage.GetGroup(groupID)
		if err != nil {
			continue
		}
		exportGroups = append(exportGroups, *group)
	}

	return exportGroups, nil
}

// ImportGroups 导入主机组配置
func (gm *GroupManager) ImportGroups(groups []data.HostGroup) (int, []error) {
	var errors []error
	successCount := 0

	// 获取现有主机组列表用于重复检查
	existingGroups, err := gm.storage.LoadGroups()
	if err != nil {
		errors = append(errors, fmt.Errorf("加载现有主机组失败: %v", err))
		return 0, errors
	}

	// 创建现有主机组名映射
	existingGroupNames := make(map[string]bool)
	for _, existing := range existingGroups {
		existingGroupNames[strings.TrimSpace(existing.Name)] = true
	}

	// 检查导入列表内部重复
	importGroupNames := make(map[string]bool)

	for _, group := range groups {
		// 检查主机组名是否与现有主机组重复
		trimmedGroupName := strings.TrimSpace(group.Name)
		if existingGroupNames[trimmedGroupName] {
			errors = append(errors, fmt.Errorf("主机组 '%s' 已存在", trimmedGroupName))
			continue
		}

		// 检查导入列表内部是否重复
		if importGroupNames[trimmedGroupName] {
			errors = append(errors, fmt.Errorf("主机组 '%s' 在导入列表中重复", trimmedGroupName))
			continue
		}

		// 生成新的ID
		group.ID = utils.GenerateID("group")

		// 检查主机组内主机名重复
		hostNames := make(map[string]bool)
		validHosts := []data.HostInfo{}

		for _, host := range group.Hosts {
			trimmedHostName := strings.TrimSpace(host.Name)
			if hostNames[trimmedHostName] {
				errors = append(errors, fmt.Errorf("主机组 '%s' 内存在重复的主机名 '%s'，跳过重复主机", trimmedGroupName, trimmedHostName))
				continue
			}
			hostNames[trimmedHostName] = true

			// 为主机组内的主机生成ID
			host.ID = utils.GenerateID("group_host")
			validHosts = append(validHosts, host)
		}

		// 更新主机组的主机列表（移除重复项）
		group.Hosts = validHosts

		// 验证主机组信息
		if validationErrors := gm.ValidateGroup(group); len(validationErrors) > 0 {
			errors = append(errors, fmt.Errorf("主机组 %s 验证失败: %v", group.Name, validationErrors))
			continue
		}

		// 验证主机组内主机信息
		if err := gm.validateGroupHosts(group.Hosts); err != nil {
			errors = append(errors, fmt.Errorf("主机组 %s 包含无效主机: %v", group.Name, err))
			continue
		}

		// 保存主机组
		if err := gm.storage.SaveGroup(group); err != nil {
			errors = append(errors, fmt.Errorf("导入主机组 %s 失败: %v", group.Name, err))
			continue
		}

		// 添加到映射中，避免后续重复
		existingGroupNames[trimmedGroupName] = true
		importGroupNames[trimmedGroupName] = true
		successCount++
	}

	return successCount, errors
}

// validateGroupInput 验证主机组输入
func (gm *GroupManager) validateGroupInput(name string, hosts []data.HostInfo) error {
	if strings.TrimSpace(name) == "" {
		return fmt.Errorf("主机组名不能为空")
	}

	if len(hosts) == 0 {
		return fmt.Errorf("主机组必须包含至少一个主机")
	}

	// 验证主机组内主机名是否重复
	hostNames := make(map[string]bool)
	for _, host := range hosts {
		trimmedName := strings.TrimSpace(host.Name)
		if trimmedName == "" {
			return fmt.Errorf("主机名不能为空")
		}
		if hostNames[trimmedName] {
			return fmt.Errorf("主机组内存在重复的主机名: %s", trimmedName)
		}
		hostNames[trimmedName] = true
	}

	return nil
}

// checkDuplicateGroupName 检查主机组名是否重复
func (gm *GroupManager) checkDuplicateGroupName(name string) error {
	groups, err := gm.storage.LoadGroups()
	if err != nil {
		return fmt.Errorf("检查主机组名重复失败: %v", err)
	}

	trimmedName := strings.TrimSpace(name)
	for _, group := range groups {
		if strings.TrimSpace(group.Name) == trimmedName {
			return fmt.Errorf("主机组名 '%s' 已存在，请使用不同的名称", trimmedName)
		}
	}

	return nil
}

// validateGroupHosts 验证主机组中的主机信息（不检查外部主机存在性）
func (gm *GroupManager) validateGroupHosts(hosts []data.HostInfo) error {
	for _, host := range hosts {
		// 只验证主机信息的完整性，不检查是否在外部主机表中存在
		if strings.TrimSpace(host.Name) == "" {
			return fmt.Errorf("主机名不能为空")
		}
		if strings.TrimSpace(host.Host) == "" {
			return fmt.Errorf("主机地址不能为空")
		}
		if strings.TrimSpace(host.Username) == "" {
			return fmt.Errorf("用户名不能为空")
		}
		if strings.TrimSpace(host.Password) == "" {
			return fmt.Errorf("密码不能为空")
		}
	}
	return nil
}

// GroupStatistics 主机组统计信息
type GroupStatistics struct {
	TotalGroups   int     `json:"totalGroups"`
	EmptyGroups   int     `json:"emptyGroups"`
	LargestGroup  int     `json:"largestGroup"`
	SmallestGroup int     `json:"smallestGroup"`
	AverageSize   float64 `json:"averageSize"`
}
