package services

import (
	"fmt"
	"strings"

	"lcheck/data"
)

// SecurityTableService 安全检查表格服务
type SecurityTableService struct{}

// NewSecurityTableService 创建安全检查表格服务
func NewSecurityTableService() *SecurityTableService {
	return &SecurityTableService{}
}

// GeneratePasswordPolicyTable 生成密码复杂度策略表格
func (sts *SecurityTableService) GeneratePasswordPolicyTable(checkResults []data.BaselineCheckResult) string {
	pwdPolicyResult := sts.findCheckResult(checkResults, "PWD_POLICY")
	if pwdPolicyResult == nil {
		return ""
	}

	// 获取实际配置值或错误信息
	minlen := "未配置"
	dcredit := "未配置"
	ucredit := "未配置"
	lcredit := "未配置"
	ocredit := "未配置"
	minclass := "未配置"
	maxrepeat := "未配置"
	maxsequence := "未配置"

	if pwdPolicyResult.Metadata != nil {
		if val, exists := pwdPolicyResult.Metadata["minlen"]; exists && val != "" {
			minlen = val
		}
		if val, exists := pwdPolicyResult.Metadata["dcredit"]; exists && val != "" {
			dcredit = val
		}
		if val, exists := pwdPolicyResult.Metadata["ucredit"]; exists && val != "" {
			ucredit = val
		}
		if val, exists := pwdPolicyResult.Metadata["lcredit"]; exists && val != "" {
			lcredit = val
		}
		if val, exists := pwdPolicyResult.Metadata["ocredit"]; exists && val != "" {
			ocredit = val
		}
		if val, exists := pwdPolicyResult.Metadata["minclass"]; exists && val != "" {
			minclass = val
		}
		if val, exists := pwdPolicyResult.Metadata["maxrepeat"]; exists && val != "" {
			maxrepeat = val
		}
		if val, exists := pwdPolicyResult.Metadata["maxsequence"]; exists && val != "" {
			maxsequence = val
		}
	}

	// 如果有错误信息，显示错误信息
	if pwdPolicyResult.Error != "" || strings.Contains(pwdPolicyResult.Output, "No such file") {
		errorMsg := pwdPolicyResult.Output
		if errorMsg == "" {
			errorMsg = pwdPolicyResult.Error
		}
		minlen = errorMsg
		dcredit = errorMsg
		ucredit = errorMsg
		lcredit = errorMsg
		ocredit = errorMsg
		minclass = errorMsg
		maxrepeat = errorMsg
		maxsequence = errorMsg
	}

	return fmt.Sprintf(`
                <h2 id="pwquality">密码复杂度策略(/etc/security/pwquality.conf)</h2>
                <table>
                    <thead>
                    <tr>
                        <th>最小长度</th>
                        <th>数字字符位数</th>
                        <th>大写字母位数</th>
                        <th>小写字母位数</th>
                        <th>特殊符号位数</th>
                        <th>最小类别数量</th>
                        <th>重复字符最大位数</th>
                        <th>连续重复字符位数</th>
                        <th>建议配置</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>
                            在/etc/security/pwquality.conf配置文件中:<br />
                            minlen = 8<br />
                            minclass = 4<br />
                            maxrepeat = 2<br />
                            maxsequence = 2
                        </td>
                    </tr>
                    </tbody>
                </table>
`, minlen, dcredit, ucredit, lcredit, ocredit, minclass, maxrepeat, maxsequence)
}

// GenerateSSHConfigTable 生成SSH配置信息表格
func (sts *SecurityTableService) GenerateSSHConfigTable(checkResults []data.BaselineCheckResult) string {
	sshConfigResult := sts.findCheckResult(checkResults, "SSH_CONFIG")
	if sshConfigResult == nil {
		return ""
	}

	// 获取实际SSH配置值
	permitRootLogin := "yes"
	passwordAuth := "yes"
	emptyPasswords := "no"
	pubkeyAuth := "yes"
	protocol := "2"
	maxAuthTries := "6"

	if sshConfigResult.Metadata != nil {
		if val, exists := sshConfigResult.Metadata["permitRootLogin"]; exists {
			permitRootLogin = val
		}
		if val, exists := sshConfigResult.Metadata["passwordAuthentication"]; exists {
			passwordAuth = val
		}
		if val, exists := sshConfigResult.Metadata["permitEmptyPasswords"]; exists {
			emptyPasswords = val
		}
		if val, exists := sshConfigResult.Metadata["pubkeyAuthentication"]; exists {
			pubkeyAuth = val
		}
		if val, exists := sshConfigResult.Metadata["protocol"]; exists {
			protocol = val
		}
		if val, exists := sshConfigResult.Metadata["maxAuthTries"]; exists {
			maxAuthTries = val
		}
	}

	// 转换为布尔值显示
	rootLoginDisplay := "false"
	if permitRootLogin == "yes" {
		rootLoginDisplay = "true"
	}

	passwordAuthDisplay := "false"
	if passwordAuth == "yes" {
		passwordAuthDisplay = "true"
	}

	emptyPasswordsDisplay := "true"
	if emptyPasswords == "no" {
		emptyPasswordsDisplay = "false"
	}

	pubkeyAuthDisplay := "false"
	if pubkeyAuth == "yes" {
		pubkeyAuthDisplay = "true"
	}

	protocolDisplay := "SSHV" + protocol

	return fmt.Sprintf(`
                <h2 id="sshd">sshd_config安全配置信息</h2>
                <table>
                    <thead>
                    <tr>
                        <th>是否可以root登录</th>
                        <th>是否允许密码进行验证</th>
                        <th>是否允许空密码进行认证</th>
                        <th>是否允许密钥免密登录</th>
                        <th>版本协议</th>
                        <th>关闭连接之前允许的最大身份验证尝试次数</th>
                        <th>建议配置</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>
                            在/etc/ssh/sshd_config配置文件中:<br />
                            PermitRootLogin no<br />
                            PasswordAuthentication yes<br />
                            PermitEmptyPasswords no<br />
                            PubkeyAuthentication yes<br />
                            Protocol 2<br />
                            MaxAuthTries 3
                        </td>
                    </tr>
                    </tbody>
                </table>
`, rootLoginDisplay, passwordAuthDisplay, emptyPasswordsDisplay, pubkeyAuthDisplay, protocolDisplay, maxAuthTries)
}

// GeneratePortStatusTable 生成端口开放状态表格
func (sts *SecurityTableService) GeneratePortStatusTable(checkResults []data.BaselineCheckResult) string {
	portResult := sts.findCheckResult(checkResults, "PORT_STATUS")
	if portResult == nil {
		return ""
	}

	var buffer strings.Builder
	buffer.WriteString(`
                <h2 id="port">端口开放状态</h2>
                <table>
                    <thead>
                    <tr>
                        <th>协议</th>
                        <th>状态</th>
                        <th>监听地址</th>
                        <th>进程信息</th>
                    </tr>
                    </thead>
                    <tbody>
`)

	// 解析端口详情并显示实际结果
	if portResult.Metadata != nil {
		if portDetails, exists := portResult.Metadata["portDetails"]; exists {
			ports := strings.Split(portDetails, ";")
			for _, port := range ports {
				if port == "" {
					continue
				}
				parts := strings.Split(port, "|")
				if len(parts) >= 4 {
					protocol := parts[0]
					state := parts[1]
					address := parts[2]
					process := parts[3]
					buffer.WriteString(fmt.Sprintf(`
                    <tr>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%s</td>
                    </tr>
`, protocol, state, address, process))
				}
			}
		}
	}

	buffer.WriteString(`
                    </tbody>
                </table>
`)

	return buffer.String()
}

// findCheckResult 查找特定ID的检查结果
func (sts *SecurityTableService) findCheckResult(checkResults []data.BaselineCheckResult, checkID string) *data.BaselineCheckResult {
	for i := range checkResults {
		if checkResults[i].ID == checkID {
			return &checkResults[i]
		}
	}
	return nil
}
