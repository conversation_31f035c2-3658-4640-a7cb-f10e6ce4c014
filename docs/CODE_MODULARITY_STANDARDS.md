# 代码模块化规范文档

## 📋 概述
本文档定义了项目的代码模块化标准，确保代码结构清晰、可维护、可扩展，避免代码冗余和功能重复。

## 🏗️ 项目结构规范

### 核心目录结构
```
lcheck/
├── main.go                 # 程序入口
├── data/                   # 数据模型层
│   ├── host_info.go       # 主机信息数据结构
│   ├── group_info.go      # 主机组数据结构
│   └── scan_result.go     # 扫描结果数据结构
├── services/              # 业务逻辑层
│   ├── host_service.go    # 主机管理服务
│   ├── group_service.go   # 主机组管理服务
│   └── scan_service.go    # 扫描服务
├── ui/                    # 用户界面层
│   ├── components/        # 可复用UI组件
│   │   ├── dialog_components.go    # 对话框组件
│   │   ├── host_table.go          # 主机表格组件
│   │   └── toolbar.go             # 工具栏组件
│   ├── host_tab.go        # 主机管理标签页
│   └── scan_tab.go        # 扫描标签页
├── utils/                 # 工具函数层
│   ├── file_utils.go      # 文件操作工具
│   ├── network_utils.go   # 网络工具
│   └── validation.go      # 数据验证工具
└── docs/                  # 文档目录
    └── CODE_MODULARITY_STANDARDS.md
```

## 🎯 模块化原则

### 1. 单一职责原则 (SRP)
- **每个模块只负责一个功能领域**
- **每个函数只做一件事情**
- **避免在一个文件中混合多种职责**

#### ✅ 正确示例：
```go
// ui/components/dialog_components.go - 只负责对话框相关功能
func ShowAddHostDialog(...)     // 添加主机对话框
func ShowEditHostDialog(...)    // 编辑主机对话框
func ShowConfirmDialog(...)     // 确认对话框
```

#### ❌ 错误示例：
```go
// 在对话框文件中混合业务逻辑
func ShowAddHostDialog(...)     // UI逻辑
func ValidateHostData(...)      // 数据验证逻辑 - 应该在utils/validation.go
func SaveHostToDatabase(...)    // 数据持久化逻辑 - 应该在services/
```

### 2. 依赖倒置原则 (DIP)
- **UI层依赖服务层接口，不直接依赖具体实现**
- **服务层依赖数据层接口**
- **使用接口定义模块间的契约**

#### ✅ 正确示例：
```go
// ui/host_tab.go
type HostService interface {
    CreateHost(name, host, username, password string, port int) (*data.HostInfo, error)
    UpdateHost(id, name, host, username, password string, port int) error
    DeleteHost(id string) error
}

type HostTab struct {
    hostService HostService  // 依赖接口，不依赖具体实现
}
```

### 3. 开闭原则 (OCP)
- **对扩展开放，对修改封闭**
- **通过接口和组合实现功能扩展**
- **避免修改现有代码来添加新功能**

## 📦 组件复用规范

### 1. UI组件复用
所有可复用的UI组件必须放在 `ui/components/` 目录下：

#### 对话框组件 (`dialog_components.go`)
```go
// 通用对话框函数，可在多处复用
func ShowSuccessDialog(title, message string, parent fyne.Window)
func ShowErrorDialog(title string, err error, parent fyne.Window)
func ShowConfirmDialog(title, message string, onConfirm func(), parent fyne.Window)
func CreateHostFormDialog(title string, host *data.HostInfo, onSave func(...), parent fyne.Window)
```

#### 表格组件 (`host_table.go`)
```go
// 可复用的表格组件
type HostTable struct {
    hosts []data.HostInfo
    list  *widget.List
}

func NewHostTable() *HostTable
func (ht *HostTable) UpdateData(hosts []data.HostInfo)
func (ht *HostTable) GetContainer() *fyne.Container
```

### 2. 服务层复用
业务逻辑必须封装在服务层，供多个UI组件复用：

```go
// services/host_service.go
type HostService struct {
    // 私有字段
}

func NewHostService() *HostService
func (hs *HostService) CreateHost(...) (*data.HostInfo, error)
func (hs *HostService) UpdateHost(...) error
func (hs *HostService) DeleteHost(id string) error
func (hs *HostService) GetAllHosts() ([]data.HostInfo, error)
func (hs *HostService) TestConnection(host data.HostInfo) *data.ConnectionResult
```

## 🚫 禁止的代码模式

### 1. 禁止在UI层直接写业务逻辑
```go
// ❌ 错误：在UI组件中直接写业务逻辑
func (ht *HostTab) addHost() {
    // 直接在UI中写数据验证和保存逻辑
    if name == "" { ... }
    // 直接操作数据库或文件
    saveToFile(host)
}

// ✅ 正确：委托给服务层
func (ht *HostTab) addHost() {
    host, err := ht.hostService.CreateHost(name, addr, user, pass, port)
    if err != nil {
        components.ShowErrorDialog("创建失败", err, ht.window)
        return
    }
    components.ShowSuccessDialog("创建成功", "主机已添加", ht.window)
}
```

### 2. 禁止重复的对话框代码
```go
// ❌ 错误：每个地方都重复创建相似的对话框
func showError1() {
    dialog := dialog.NewInformation("错误", "操作失败", window)
    dialog.Show()
}

func showError2() {
    dialog := dialog.NewInformation("错误", "保存失败", window)
    dialog.Show()
}

// ✅ 正确：使用统一的组件
func showError1() {
    components.ShowErrorDialog("错误", fmt.Errorf("操作失败"), window)
}

func showError2() {
    components.ShowErrorDialog("错误", fmt.Errorf("保存失败"), window)
}
```

### 3. 禁止硬编码的UI尺寸和样式
```go
// ❌ 错误：硬编码尺寸
dialog.Resize(fyne.NewSize(350, 400))
entry.Resize(fyne.NewSize(250, 30))

// ✅ 正确：使用常量或配置
const (
    DialogWidth  = 350
    DialogHeight = 400
    EntryWidth   = 250
    EntryHeight  = 30
)

dialog.Resize(fyne.NewSize(DialogWidth, DialogHeight))
```

## 📏 命名规范

### 1. 文件命名
- **功能模块_类型.go** 格式
- 例如：`host_service.go`, `dialog_components.go`, `host_table.go`

### 2. 函数命名
- **公共函数**：大写开头，清晰描述功能
- **私有函数**：小写开头，简洁明了
- **构造函数**：使用 `New` 前缀

### 3. 接口命名
- **以 `er` 结尾**：如 `HostManager`, `DataValidator`
- **或以 `Service` 结尾**：如 `HostService`, `ScanService`

## 🔄 重构指导原则

### 何时需要重构
1. **发现重复代码** - 立即提取为公共函数或组件
2. **函数超过50行** - 考虑拆分为多个小函数
3. **文件超过500行** - 考虑按功能拆分为多个文件
4. **参数超过5个** - 考虑使用结构体封装参数

### 重构步骤
1. **识别重复代码**
2. **提取公共函数**
3. **创建合适的接口**
4. **更新调用方**
5. **编写测试验证**

## ✅ 代码审查检查清单

### 模块化检查
- [ ] 每个文件职责单一
- [ ] 没有重复的业务逻辑
- [ ] UI组件可复用
- [ ] 服务层接口清晰
- [ ] 依赖关系正确

### 代码质量检查
- [ ] 函数长度合理（<50行）
- [ ] 参数数量合理（<5个）
- [ ] 命名清晰易懂
- [ ] 错误处理完善
- [ ] 注释充分

## 📚 最佳实践示例

### 创建新功能的标准流程
1. **定义数据结构** (`data/`)
2. **实现服务接口** (`services/`)
3. **创建UI组件** (`ui/components/`)
4. **集成到主界面** (`ui/`)
5. **添加工具函数** (`utils/`)

## 🛠️ 实用工具和模板

### 常用组件模板

#### 1. 服务层模板
```go
// services/example_service.go
package services

import "lcheck/data"

type ExampleService struct {
    // 私有字段
}

func NewExampleService() *ExampleService {
    return &ExampleService{}
}

func (es *ExampleService) Create(params ...) (*data.Example, error) {
    // 1. 参数验证
    // 2. 业务逻辑处理
    // 3. 数据持久化
    // 4. 返回结果
}

func (es *ExampleService) Update(id string, params ...) error {
    // 更新逻辑
}

func (es *ExampleService) Delete(id string) error {
    // 删除逻辑
}

func (es *ExampleService) GetAll() ([]data.Example, error) {
    // 查询逻辑
}
```

#### 2. UI组件模板
```go
// ui/components/example_component.go
package components

import (
    "fyne.io/fyne/v2"
    "fyne.io/fyne/v2/widget"
)

type ExampleComponent struct {
    container *fyne.Container
    // 其他私有字段
}

func NewExampleComponent() *ExampleComponent {
    ec := &ExampleComponent{}
    ec.createUI()
    return ec
}

func (ec *ExampleComponent) createUI() {
    // UI创建逻辑
}

func (ec *ExampleComponent) GetContainer() *fyne.Container {
    return ec.container
}

func (ec *ExampleComponent) UpdateData(data interface{}) {
    // 数据更新逻辑
}
```

### 错误处理规范

#### 统一错误处理
```go
// utils/errors.go
package utils

import "fmt"

type AppError struct {
    Code    string
    Message string
    Cause   error
}

func (e *AppError) Error() string {
    if e.Cause != nil {
        return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
    }
    return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func NewAppError(code, message string, cause error) *AppError {
    return &AppError{
        Code:    code,
        Message: message,
        Cause:   cause,
    }
}

// 预定义错误类型
var (
    ErrHostNotFound    = NewAppError("HOST_NOT_FOUND", "主机不存在", nil)
    ErrInvalidInput    = NewAppError("INVALID_INPUT", "输入参数无效", nil)
    ErrConnectionFailed = NewAppError("CONNECTION_FAILED", "连接失败", nil)
)
```

## 📋 开发检查清单

### 新功能开发前
- [ ] 确认功能需求和设计
- [ ] 检查是否有现有组件可复用
- [ ] 确定数据结构和接口设计
- [ ] 规划模块间的依赖关系

### 开发过程中
- [ ] 遵循单一职责原则
- [ ] 使用统一的错误处理
- [ ] 避免硬编码常量
- [ ] 及时提取重复代码

### 开发完成后
- [ ] 代码审查检查清单
- [ ] 测试功能完整性
- [ ] 更新相关文档
- [ ] 确认没有引入新的依赖问题

## 🔧 重构工具和技巧

### 代码重复检测
```bash
# 使用工具检测重复代码
go install github.com/mibk/dupl@latest
dupl -threshold 50 ./...
```

### 依赖分析
```bash
# 分析模块依赖关系
go mod graph | grep lcheck
```

### 代码复杂度检测
```bash
# 检测函数复杂度
go install github.com/fzipp/gocyclo/cmd/gocyclo@latest
gocyclo -over 10 .
```

## 📈 持续改进

### 定期审查
- **每月代码审查** - 检查是否有新的重复代码
- **季度架构审查** - 评估模块化程度和依赖关系
- **年度重构计划** - 制定大规模重构和优化计划

### 指标监控
- **代码重复率** - 目标 < 5%
- **函数平均长度** - 目标 < 30行
- **文件平均长度** - 目标 < 300行
- **模块耦合度** - 目标：低耦合高内聚

---

**📝 注意：** 这个规范是活文档，随着项目发展需要持续更新和完善。所有开发者都有责任遵循和改进这些规范。

**🎯 目标：** 通过严格遵循这些规范，确保代码质量、可维护性和团队协作效率。
